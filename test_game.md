# 🧪 Block Blast Testing Guide

## **Step 1: Open the Game**
1. Open `Block Blast.rbxlx` in Roblox Studio
2. Press **F5** or click the **Play** button

## **Step 2: Check the Output**
1. Open **View > Output** window
2. Look for these messages:
   ```
   🎮 Starting Block Blast initialization...
   ✅ GameManager initialized
   Created StarterGui
   Created Normal
   Created NormalGamePlay
   Created Grid with layout
   Created 64 grid tiles
   Created CurrentScore label
   Created HighestScore label
   Block Blast game initialized!
   ```

## **Step 3: Check the UI**
You should see:
- **Dark background** (main game area)
- **8x8 grid** in the center (light gray squares)
- **"Score: 0"** in top-left
- **"Best: 0"** in top-right
- **3 colored block shapes** at the bottom
- **Red "Reset Game" button** at the top-center

## **Step 4: Test Gameplay**
1. **Click and drag** one of the 3 shapes from the bottom
2. **Drop it** on the grid
3. **Watch** the score increase
4. **Try to fill** a complete row or column
5. **See** the line disappear and bonus points

## **🚨 If Nothing Shows Up:**

### **Check 1: Script Errors**
- Look in Output for **red error messages**
- Common error: "attempt to index nil with..."

### **Check 2: UI Missing**
- Check **Players > [YourName] > PlayerGui**
- Should see **StarterGui** folder

### **Check 3: Rojo Connection**
- Run `rojo serve` in terminal
- Connect in Studio (Plugins > Rojo > Connect)

### **Check 4: Manual UI Creation**
If automatic creation fails, manually create:
1. **ScreenGui** in StarterGui named "Normal"
2. **Frame** in Normal named "NormalGamePlay"
3. **Frame** in NormalGamePlay named "Grid"

## **🎯 Expected Behavior:**
- ✅ Grid appears with 64 tiles
- ✅ 3 random shapes appear at bottom
- ✅ Shapes can be dragged and placed
- ✅ Score updates when placing blocks
- ✅ Lines clear when complete
- ✅ Game over when no moves possible
- ✅ Reset button works

## **📞 If Still Not Working:**
Share the **Output window contents** - that will show exactly what's failing!
