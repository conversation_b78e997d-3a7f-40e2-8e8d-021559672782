<roblox version="4">
  <Item class="Lighting" referent="0">
    <Properties>
      <string name="Name">Lighting</string>
      <Color3 name="Ambient">
        <R>0</R>
        <G>0</G>
        <B>0</B>
      </Color3>
      <float name="Brightness">2</float>
      <bool name="GlobalShadows">true</bool>
      <bool name="Outlines">false</bool>
      <token name="Technology">1</token>
    </Properties>
  </Item>
  <Item class="ReplicatedStorage" referent="1">
    <Properties>
      <string name="Name">ReplicatedStorage</string>
    </Properties>
    <Item class="Folder" referent="2">
      <Properties>
        <string name="Name">Shared</string>
      </Properties>
      <Item class="ModuleScript" referent="3">
        <Properties>
          <string name="Name">BlockShapes</string>
          <string name="Source"><![CDATA[-- Block Shapes for Block Blast Game
local BlockShapes = {}

-- Define all possible block shapes (similar to Tetris pieces)
BlockShapes.SHAPES = {
	-- Single block
	{
		name = "Single",
		pattern = {
			{1}
		},
		color = Color3.fromRGB(255, 100, 100)
	},
	
	-- 2x1 Line
	{
		name = "Line2",
		pattern = {
			{1, 1}
		},
		color = Color3.fromRGB(100, 255, 100)
	},
	
	-- 3x1 Line
	{
		name = "Line3",
		pattern = {
			{1, 1, 1}
		},
		color = Color3.fromRGB(100, 100, 255)
	},
	
	-- 4x1 Line
	{
		name = "Line4",
		pattern = {
			{1, 1, 1, 1}
		},
		color = Color3.fromRGB(255, 255, 100)
	},
	
	-- 5x1 Line
	{
		name = "Line5",
		pattern = {
			{1, 1, 1, 1, 1}
		},
		color = Color3.fromRGB(255, 100, 255)
	},
	
	-- 2x2 Square
	{
		name = "Square2x2",
		pattern = {
			{1, 1},
			{1, 1}
		},
		color = Color3.fromRGB(100, 255, 255)
	},
	
	-- 3x3 Square
	{
		name = "Square3x3",
		pattern = {
			{1, 1, 1},
			{1, 1, 1},
			{1, 1, 1}
		},
		color = Color3.fromRGB(255, 150, 100)
	},
	
	-- L-Shape
	{
		name = "L_Shape",
		pattern = {
			{1, 0},
			{1, 0},
			{1, 1}
		},
		color = Color3.fromRGB(150, 255, 150)
	},
	
	-- Reverse L-Shape
	{
		name = "L_Reverse",
		pattern = {
			{0, 1},
			{0, 1},
			{1, 1}
		},
		color = Color3.fromRGB(255, 150, 255)
	},
	
	-- T-Shape
	{
		name = "T_Shape",
		pattern = {
			{1, 1, 1},
			{0, 1, 0}
		},
		color = Color3.fromRGB(150, 150, 255)
	},
	
	-- Small L
	{
		name = "Small_L",
		pattern = {
			{1, 0},
			{1, 1}
		},
		color = Color3.fromRGB(255, 200, 150)
	},
	
	-- Corner piece
	{
		name = "Corner",
		pattern = {
			{1, 1},
			{1, 0}
		},
		color = Color3.fromRGB(200, 255, 200)
	}
}

-- Generate a random block shape
function BlockShapes.getRandomShape()
	local randomIndex = math.random(1, #BlockShapes.SHAPES)
	return BlockShapes.SHAPES[randomIndex]
end

-- Generate 3 random shapes for the player to choose from
function BlockShapes.generateThreeShapes()
	local shapes = {}
	for i = 1, 3 do
		shapes[i] = BlockShapes.getRandomShape()
	end
	return shapes
end

-- Get the size of a shape (width, height)
function BlockShapes.getShapeSize(shape)
	local height = #shape.pattern
	local width = 0
	
	for _, row in ipairs(shape.pattern) do
		width = math.max(width, #row)
	end
	
	return width, height
end

-- Check if a shape can fit at a specific position on the grid
function BlockShapes.canPlaceShape(gridState, shape, startRow, startCol)
	local width, height = BlockShapes.getShapeSize(shape)
	
	-- Check bounds
	if startRow + height - 1 > 8 or startCol + width - 1 > 8 then
		return false
	end
	
	-- Check for collisions
	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1
				
				if gridState[gridRow] and gridState[gridRow][gridCol] == 1 then
					return false
				end
			end
		end
	end
	
	return true
end

return BlockShapes
]]></string>
        </Properties>
      </Item>
      <Item class="ModuleScript" referent="4">
        <Properties>
          <string name="Name">GameManager</string>
          <string name="Source"><![CDATA[-- Game Manager for Block Blast
local GameManager = {}

-- Game state
GameManager.currentScore = 0
GameManager.highestScore = 0
GameManager.gridState = {}
GameManager.availableShapes = {}
GameManager.gameActive = true

-- Initialize the game
function GameManager.init()
	-- Initialize 8x8 grid state (0 = empty, 1 = filled)
	GameManager.gridState = {}
	for row = 1, 8 do
		GameManager.gridState[row] = {}
		for col = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
	end
	
	-- Load highest score from DataStore (placeholder for now)
	GameManager.highestScore = 0
	GameManager.currentScore = 0
	GameManager.gameActive = true
	
	-- Generate initial shapes
	local BlockShapes = require(script.Parent.BlockShapes)
	GameManager.availableShapes = BlockShapes.generateThreeShapes()
end

-- Place a shape on the grid
function GameManager.placeShape(shape, startRow, startCol)
	local BlockShapes = require(script.Parent.BlockShapes)

	-- Check if placement is valid
	if not BlockShapes.canPlaceShape(GameManager.gridState, shape, startRow, startCol) then
		return false
	end
	
	-- Place the shape
	local width, height = BlockShapes.getShapeSize(shape)

	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1
				GameManager.gridState[gridRow][gridCol] = 1
			end
		end
	end
	
	-- Check for line clears
	GameManager.checkAndClearLines()
	
	-- Generate new shape if this was one of the available shapes
	GameManager.replaceUsedShape(shape)
	
	return true
end

-- Check and clear complete lines (rows and columns)
function GameManager.checkAndClearLines()
	local linesCleared = 0
	local rowsToCheck = {}
	local colsToCheck = {}
	
	-- Check for complete rows
	for row = 1, 8 do
		local isComplete = true
		for col = 1, 8 do
			if GameManager.gridState[row][col] == 0 then
				isComplete = false
				break
			end
		end
		if isComplete then
			table.insert(rowsToCheck, row)
		end
	end
	
	-- Check for complete columns
	for col = 1, 8 do
		local isComplete = true
		for row = 1, 8 do
			if GameManager.gridState[row][col] == 0 then
				isComplete = false
				break
			end
		end
		if isComplete then
			table.insert(colsToCheck, col)
		end
	end
	
	-- Clear complete rows
	for _, row in ipairs(rowsToCheck) do
		for col = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
		linesCleared = linesCleared + 1
	end
	
	-- Clear complete columns
	for _, col in ipairs(colsToCheck) do
		for row = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
		linesCleared = linesCleared + 1
	end
	
	-- Award points for clearing lines
	if linesCleared > 0 then
		local points = linesCleared * 100
		if linesCleared > 1 then
			points = points * 2
		end
		GameManager.addScore(points)
	end
	
	return linesCleared
end

-- Add score and update highest score
function GameManager.addScore(points)
	GameManager.currentScore = GameManager.currentScore + points
	
	if GameManager.currentScore > GameManager.highestScore then
		GameManager.highestScore = GameManager.currentScore
		-- TODO: Save to DataStore
	end
end

-- Replace a used shape with a new random one
function GameManager.replaceUsedShape(usedShape)
	local BlockShapes = require(script.Parent.BlockShapes)

	-- Find and replace the used shape immediately
	for i, shape in ipairs(GameManager.availableShapes) do
		if shape == usedShape then
			GameManager.availableShapes[i] = BlockShapes.getRandomShape()
			break
		end
	end

	-- If all 3 shapes are used, generate 3 new ones
	local allUsed = true
	for _, shape in ipairs(GameManager.availableShapes) do
		if shape then
			allUsed = false
			break
		end
	end

	if allUsed then
		GameManager.availableShapes = BlockShapes.generateThreeShapes()
	end

	-- Check if any shapes can still be placed (game over condition)
	GameManager.checkGameOver()
end

-- Check if the game is over (no more valid moves)
function GameManager.checkGameOver()
	local BlockShapes = require(script.Parent.BlockShapes)
	
	for _, shape in ipairs(GameManager.availableShapes) do
		-- Try to place this shape anywhere on the grid
		for row = 1, 8 do
			for col = 1, 8 do
				if BlockShapes.canPlaceShape(GameManager.gridState, shape, row, col) then
					return false -- Game is not over
				end
			end
		end
	end
	
	-- No valid moves found
	GameManager.gameActive = false
	return true
end

-- Reset the game
function GameManager.resetGame()
	GameManager.init()
end

-- Get current game state for UI updates
function GameManager.getGameState()
	return {
		currentScore = GameManager.currentScore,
		highestScore = GameManager.highestScore,
		gridState = GameManager.gridState,
		availableShapes = GameManager.availableShapes,
		gameActive = GameManager.gameActive
	}
end

return GameManager
]]></string>
        </Properties>
      </Item>
      <Item class="ModuleScript" referent="5">
        <Properties>
          <string name="Name">ParticleEffects</string>
          <string name="Source"><![CDATA[local TweenService = game:GetService("TweenService")
local ParticleEffects = {}

function ParticleEffects.createExplosion(parent, position, color, particleCount)
	particleCount = particleCount or 8
	
	for i = 1, particleCount do
		local particle = Instance.new("Frame")
		particle.Size = UDim2.new(0, math.random(4, 8), 0, math.random(4, 8))
		particle.Position = position
		particle.BackgroundColor3 = color
		particle.BorderSizePixel = 0
		particle.Parent = parent
		
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(1, 0)
		corner.Parent = particle
		
		local angle = (i / particleCount) * math.pi * 2
		local distance = math.random(20, 40)
		local endX = math.cos(angle) * distance
		local endY = math.sin(angle) * distance
		
		local tween = TweenService:Create(
			particle,
			TweenInfo.new(
				math.random(50, 80) / 100,
				Enum.EasingStyle.Quad,
				Enum.EasingDirection.Out
			),
			{
				Position = position + UDim2.new(0, endX, 0, endY),
				BackgroundTransparency = 1,
				Size = UDim2.new(0, 2, 0, 2)
			}
		)
		
		tween:Play()
		tween.Completed:Connect(function()
			particle:Destroy()
		end)
	end
end

function ParticleEffects.createStarBurst(parent, position, color, starCount)
	starCount = starCount or 6
	
	for i = 1, starCount do
		local star = Instance.new("Frame")
		star.Size = UDim2.new(0, 6, 0, 6)
		star.Position = position
		star.BackgroundColor3 = color
		star.BorderSizePixel = 0
		star.Parent = parent
		
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(0, 1)
		corner.Parent = star
		
		local angle = (i / starCount) * math.pi * 2
		local distance = math.random(30, 60)
		local endX = math.cos(angle) * distance
		local endY = math.sin(angle) * distance
		
		local tween = TweenService:Create(
			star,
			TweenInfo.new(
				0.8,
				Enum.EasingStyle.Quart,
				Enum.EasingDirection.Out
			),
			{
				Position = position + UDim2.new(0, endX, 0, endY),
				BackgroundTransparency = 1,
				Rotation = math.random(0, 360)
			}
		)
		
		tween:Play()
		tween.Completed:Connect(function()
			star:Destroy()
		end)
	end
end

function ParticleEffects.createRipple(parent, position, color)
	local ripple = Instance.new("Frame")
	ripple.Size = UDim2.new(0, 10, 0, 10)
	ripple.Position = position + UDim2.new(0, -5, 0, -5)
	ripple.BackgroundColor3 = color
	ripple.BackgroundTransparency = 0.5
	ripple.BorderSizePixel = 0
	ripple.Parent = parent
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(1, 0)
	corner.Parent = ripple
	
	local tween = TweenService:Create(
		ripple,
		TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Size = UDim2.new(0, 50, 0, 50),
			Position = position + UDim2.new(0, -25, 0, -25),
			BackgroundTransparency = 1
		}
	)
	
	tween:Play()
	tween.Completed:Connect(function()
		ripple:Destroy()
	end)
end

function ParticleEffects.createSparkles(parent, position, color, sparkleCount)
	sparkleCount = sparkleCount or 12
	
	for i = 1, sparkleCount do
		local sparkle = Instance.new("Frame")
		sparkle.Size = UDim2.new(0, 3, 0, 3)
		sparkle.Position = position + UDim2.new(0, math.random(-10, 10), 0, math.random(-10, 10))
		sparkle.BackgroundColor3 = color
		sparkle.BorderSizePixel = 0
		sparkle.Parent = parent
		
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(1, 0)
		corner.Parent = sparkle
		
		local tween = TweenService:Create(
			sparkle,
			TweenInfo.new(
				math.random(30, 60) / 100,
				Enum.EasingStyle.Sine,
				Enum.EasingDirection.Out
			),
			{
				Position = sparkle.Position + UDim2.new(0, math.random(-20, 20), 0, math.random(-30, -10)),
				BackgroundTransparency = 1
			}
		)
		
		tween:Play()
		tween.Completed:Connect(function()
			sparkle:Destroy()
		end)
	end
end

function ParticleEffects.createComboEffect(parent, position, color, comboLevel)
	ParticleEffects.createExplosion(parent, position, color, 12)
	
	wait(0.1)
	ParticleEffects.createStarBurst(parent, position, color, 8)
	
	if comboLevel > 1 then
		wait(0.1)
		ParticleEffects.createRipple(parent, position, color)
	end
	
	if comboLevel > 2 then
		wait(0.1)
		ParticleEffects.createSparkles(parent, position, color, 20)
	end
end

function ParticleEffects.createLineBreakEffect(parent, tiles, color)
	for _, tile in ipairs(tiles) do
		local tilePos = UDim2.new(0.5, 0, 0.5, 0)
		
		ParticleEffects.createExplosion(tile, tilePos, color, 6)
		
		wait(0.05)
	end
end

return ParticleEffects
]]></string>
        </Properties>
      </Item>
    </Item>
  </Item>
  <Item class="ServerScriptService" referent="6">
    <Properties>
      <string name="Name">ServerScriptService</string>
    </Properties>
    <Item class="Script" referent="7">
      <Properties>
        <string name="Name">Server</string>
        <token name="RunContext">0</token>
        <string name="Source">local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local DataStoreManager = require(script.DataStoreManager)

local remoteEvents = Instance.new("Folder")
remoteEvents.Name = "RemoteEvents"
remoteEvents.Parent = ReplicatedStorage

local updateScoreEvent = Instance.new("RemoteEvent")
updateScoreEvent.Name = "UpdateScore"
updateScoreEvent.Parent = remoteEvents

local getLeaderboardEvent = Instance.new("RemoteFunction")
getLeaderboardEvent.Name = "GetLeaderboard"
getLeaderboardEvent.Parent = remoteEvents

local getPlayerDataEvent = Instance.new("RemoteFunction")
getPlayerDataEvent.Name = "GetPlayerData"
getPlayerDataEvent.Parent = remoteEvents

local setThemeEvent = Instance.new("RemoteEvent")
setThemeEvent.Name = "SetTheme"
setThemeEvent.Parent = remoteEvents

updateScoreEvent.OnServerEvent:Connect(function(player, gameData)
	DataStoreManager.updateStats(player, gameData)
	if gameData.finalScore then
		DataStoreManager.updateHighScore(player, gameData.finalScore)
	end
end)

getLeaderboardEvent.OnServerInvoke = function(player)
	return DataStoreManager.getLeaderboard(10)
end

getPlayerDataEvent.OnServerInvoke = function(player)
	return DataStoreManager.getPlayerData(player)
end

setThemeEvent.OnServerEvent:Connect(function(player, themeName)
	DataStoreManager.setTheme(player, themeName)
end)

print("Block Blast server initialized!")</string>
      </Properties>
      <Item class="ModuleScript" referent="8">
        <Properties>
          <string name="Name">DataStoreManager</string>
          <string name="Source"><![CDATA[local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local playerDataStore = DataStoreService:GetDataStore("BlockBlastPlayerData")
local leaderboardStore = DataStoreService:GetOrderedDataStore("BlockBlastLeaderboard")

local DataStoreManager = {}

local defaultData = {
	highestScore = 0,
	totalGamesPlayed = 0,
	totalLinesCleared = 0,
	totalBlocksPlaced = 0,
	selectedTheme = "Default",
	achievements = {},
	statistics = {
		bestStreak = 0,
		totalPlayTime = 0,
		averageScore = 0
	}
}

local playerData = {}

function DataStoreManager.loadPlayerData(player)
	local success, data = pcall(function()
		return playerDataStore:GetAsync(player.UserId)
	end)
	
	if success and data then
		playerData[player.UserId] = data
		print("Loaded data for", player.Name)
	else
		playerData[player.UserId] = defaultData
		print("Created new data for", player.Name)
	end
	
	return playerData[player.UserId]
end

function DataStoreManager.savePlayerData(player)
	if not playerData[player.UserId] then return end
	
	local success, error = pcall(function()
		playerDataStore:SetAsync(player.UserId, playerData[player.UserId])
	end)
	
	if success then
		print("Saved data for", player.Name)
	else
		warn("Failed to save data for", player.Name, ":", error)
	end
end

function DataStoreManager.updateHighScore(player, newScore)
	if not playerData[player.UserId] then return end
	
	local data = playerData[player.UserId]
	if newScore > data.highestScore then
		data.highestScore = newScore
		DataStoreManager.savePlayerData(player)
		DataStoreManager.updateLeaderboard(player, newScore)
		return true
	end
	return false
end

function DataStoreManager.updateStats(player, gameData)
	if not playerData[player.UserId] then return end
	
	local data = playerData[player.UserId]
	data.totalGamesPlayed = data.totalGamesPlayed + 1
	data.totalLinesCleared = data.totalLinesCleared + (gameData.linesCleared or 0)
	data.totalBlocksPlaced = data.totalBlocksPlaced + (gameData.blocksPlaced or 0)
	
	if gameData.streak and gameData.streak > data.statistics.bestStreak then
		data.statistics.bestStreak = gameData.streak
	end
	
	data.statistics.averageScore = math.floor(
		(data.statistics.averageScore * (data.totalGamesPlayed - 1) + gameData.finalScore) / data.totalGamesPlayed
	)
	
	DataStoreManager.savePlayerData(player)
end

function DataStoreManager.updateLeaderboard(player, score)
	local success, error = pcall(function()
		leaderboardStore:SetAsync(player.UserId, score)
	end)
	
	if not success then
		warn("Failed to update leaderboard for", player.Name, ":", error)
	end
end

function DataStoreManager.getLeaderboard(count)
	count = count or 10
	local success, pages = pcall(function()
		return leaderboardStore:GetSortedAsync(false, count)
	end)
	
	if success then
		local leaderboard = {}
		local data = pages:GetCurrentPage()
		
		for rank, entry in ipairs(data) do
			local playerName = "Unknown"
			local playerSuccess, player = pcall(function()
				return Players:GetNameFromUserIdAsync(entry.key)
			end)
			
			if playerSuccess then
				playerName = player
			end
			
			table.insert(leaderboard, {
				rank = rank,
				name = playerName,
				score = entry.value,
				userId = entry.key
			})
		end
		
		return leaderboard
	else
		warn("Failed to get leaderboard:", pages)
		return {}
	end
end

function DataStoreManager.getPlayerData(player)
	return playerData[player.UserId] or defaultData
end

function DataStoreManager.setTheme(player, themeName)
	if not playerData[player.UserId] then return end
	
	playerData[player.UserId].selectedTheme = themeName
	DataStoreManager.savePlayerData(player)
end

Players.PlayerAdded:Connect(function(player)
	DataStoreManager.loadPlayerData(player)
end)

Players.PlayerRemoving:Connect(function(player)
	DataStoreManager.savePlayerData(player)
	playerData[player.UserId] = nil
end)

game:BindToClose(function()
	for userId, data in pairs(playerData) do
		local player = Players:GetPlayerByUserId(userId)
		if player then
			DataStoreManager.savePlayerData(player)
		end
	end
end)

return DataStoreManager
]]></string>
        </Properties>
      </Item>
    </Item>
  </Item>
  <Item class="SoundService" referent="9">
    <Properties>
      <string name="Name">SoundService</string>
      <bool name="RespectFilteringEnabled">true</bool>
    </Properties>
  </Item>
  <Item class="StarterPlayer" referent="10">
    <Properties>
      <string name="Name">StarterPlayer</string>
    </Properties>
    <Item class="StarterPlayerScripts" referent="11">
      <Properties>
        <string name="Name">StarterPlayerScripts</string>
      </Properties>
      <Item class="LocalScript" referent="12">
        <Properties>
          <string name="Name">Client</string>
          <string name="Source">local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local updateScoreEvent = remoteEvents:WaitForChild("UpdateScore")
local getLeaderboardEvent = remoteEvents:WaitForChild("GetLeaderboard")
local getPlayerDataEvent = remoteEvents:WaitForChild("GetPlayerData")
local setThemeEvent = remoteEvents:WaitForChild("SetTheme")

local BlockShapes = require(ReplicatedStorage.Shared.BlockShapes)
local GameManager = require(ReplicatedStorage.Shared.GameManager)
local ParticleEffects = require(ReplicatedStorage.Shared.ParticleEffects)
local LeaderboardUI = require(script.LeaderboardUI)

GameManager.init()

local normalUI = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay")
local currentScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("CurrentScore")
local highestScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("HighestScore")
local grid = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("Grid")



local gameStats = {
	linesCleared = 0,
	blocksPlaced = 0,
	startTime = tick()
}

local shapeSelection = normalUI:FindFirstChild("ShapeSelection")
if not shapeSelection then
	shapeSelection = Instance.new("Frame")
	shapeSelection.Name = "ShapeSelection"
	shapeSelection.Size = UDim2.new(0.8, 0, 0.15, 0)
	shapeSelection.Position = UDim2.new(0.1, 0, 0.8, 0)
	shapeSelection.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	shapeSelection.BorderSizePixel = 0
	shapeSelection.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = shapeSelection

	local layout = Instance.new("UIListLayout")
	layout.FillDirection = Enum.FillDirection.Horizontal
	layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	layout.VerticalAlignment = Enum.VerticalAlignment.Center
	layout.Padding = UDim.new(0, 20)
	layout.Parent = shapeSelection
end

local leaderboardFrame, leaderboardScrollFrame = LeaderboardUI.createLeaderboardFrame(normalUI)



local leaderboardButton = normalUI:FindFirstChild("LeaderboardButton")
if not leaderboardButton then
	leaderboardButton = Instance.new("TextButton")
	leaderboardButton.Name = "LeaderboardButton"
	leaderboardButton.Size = UDim2.new(0.1, 0, 0.06, 0)
	leaderboardButton.Position = UDim2.new(0.85, 0, 0.15, 0)
	leaderboardButton.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
	leaderboardButton.Text = "🏆"
	leaderboardButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	leaderboardButton.TextScaled = true
	leaderboardButton.Font = Enum.Font.SourceSansBold
	leaderboardButton.BorderSizePixel = 0
	leaderboardButton.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = leaderboardButton
end

local draggedShape = nil
local draggedShapeUI = nil
local isDragging = false
local previewTiles = {}

local function createScorePopup(points)
	local popup = Instance.new("TextLabel")
	popup.Size = UDim2.new(0, 100, 0, 40)
	popup.Position = UDim2.new(0.5, -50, 0.4, 0)
	popup.BackgroundTransparency = 1
	popup.Text = "+" .. points
	popup.TextColor3 = Color3.fromRGB(255, 255, 100)
	popup.TextScaled = true
	popup.Font = Enum.Font.SourceSansBold
	popup.TextStrokeTransparency = 0
	popup.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
	popup.Parent = normalUI

	local tween = game:GetService("TweenService"):Create(
		popup,
		TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Position = UDim2.new(0.5, -50, 0.2, 0),
			TextTransparency = 1,
			TextStrokeTransparency = 1
		}
	)

	tween:Play()
	tween.Completed:Connect(function()
		popup:Destroy()
	end)
end

local lastScore = 0

local function updateScoreDisplay()
	local gameState = GameManager.getGameState()
	if currentScoreLabel then
		currentScoreLabel.Text = "Score: " .. gameState.currentScore

		if gameState.currentScore > lastScore then
			local pointsGained = gameState.currentScore - lastScore
			if pointsGained >= 100 then
				createScorePopup(pointsGained)
			end
		end
		lastScore = gameState.currentScore
	end
	if highestScoreLabel then
		highestScoreLabel.Text = "Best: " .. gameState.highestScore
	end
end

local function updateGridVisual()
	local gameState = GameManager.getGameState()
	for row = 1, 8 do
		for col = 1, 8 do
			local tileIndex = (row - 1) * 8 + col
			local tile = grid:FindFirstChild("Tile_" .. tileIndex)
			if tile then
				if gameState.gridState[row][col] == 1 then
					tile.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
					tile.BackgroundTransparency = 0
				else
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
end

local function createEnhancedParticleEffect(tile, linesCleared)
	local tilePos = UDim2.new(0.5, 0, 0.5, 0)
	local color = Color3.fromRGB(255, 255, 100)

	ParticleEffects.createExplosion(tile, tilePos, color, 8)

	if linesCleared > 1 then
		wait(0.1)
		ParticleEffects.createStarBurst(tile, tilePos, color, 6)
	end

	if linesCleared > 2 then
		wait(0.1)
		ParticleEffects.createSparkles(tile, tilePos, color, 15)
	end
end

local function flashClearedLines()
	local gameState = GameManager.getGameState()
	local clearedTiles = {}

	for row = 1, 8 do
		local isRowComplete = true
		for col = 1, 8 do
			if gameState.gridState[row][col] == 0 then
				isRowComplete = false
				break
			end
		end
		if isRowComplete then
			for col = 1, 8 do
				local tileIndex = (row - 1) * 8 + col
				local tile = grid:FindFirstChild("Tile_" .. tileIndex)
				if tile then
					tile.BackgroundColor3 = Color3.fromRGB(255, 255, 100)
					table.insert(clearedTiles, tile)
				end
			end
		end
	end

	for col = 1, 8 do
		local isColComplete = true
		for row = 1, 8 do
			if gameState.gridState[row][col] == 0 then
				isColComplete = false
				break
			end
		end
		if isColComplete then
			for row = 1, 8 do
				local tileIndex = (row - 1) * 8 + col
				local tile = grid:FindFirstChild("Tile_" .. tileIndex)
				if tile then
					tile.BackgroundColor3 = Color3.fromRGB(255, 255, 100)
					table.insert(clearedTiles, tile)
				end
			end
		end
	end

	wait(0.1)

	for _, tile in ipairs(clearedTiles) do
		createParticleEffect(tile)

		local shakeTween = game:GetService("TweenService"):Create(
			tile,
			TweenInfo.new(0.1, Enum.EasingStyle.Bounce, Enum.EasingDirection.InOut, 0, true),
			{Position = tile.Position + UDim2.new(0, 2, 0, 0)}
		)
		shakeTween:Play()

		local colorTween = game:GetService("TweenService"):Create(
			tile,
			TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				BackgroundColor3 = Color3.fromRGB(255, 100, 100),
				BackgroundTransparency = 0.8
			}
		)
		colorTween:Play()
	end

	wait(0.2)
end

local function createShapeUI(shape, index)
	local shapeFrame = Instance.new("Frame")
	shapeFrame.Name = "Shape_" .. index
	shapeFrame.Size = UDim2.new(0, 80, 0, 80)
	shapeFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
	shapeFrame.BorderSizePixel = 0
	shapeFrame.Parent = shapeSelection

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = shapeFrame

	local shapeGrid = Instance.new("Frame")
	shapeGrid.Size = UDim2.new(0.8, 0, 0.8, 0)
	shapeGrid.Position = UDim2.new(0.1, 0, 0.1, 0)
	shapeGrid.BackgroundTransparency = 1
	shapeGrid.Parent = shapeFrame

	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellPadding = UDim2.new(0, 1, 0, 1)
	gridLayout.Parent = shapeGrid

	local width, height = BlockShapes.getShapeSize(shape)
	gridLayout.CellSize = UDim2.new(1/width, -1, 1/height, -1)
	gridLayout.FillDirectionMaxCells = width

	for row = 1, height do
		for col = 1, width do
			local cell = Instance.new("Frame")
			cell.BorderSizePixel = 0
			cell.Parent = shapeGrid

			if shape.pattern[row][col] == 1 then
				cell.BackgroundColor3 = shape.color
				cell.BackgroundTransparency = 0
			else
				cell.BackgroundTransparency = 1
			end

			local cellCorner = Instance.new("UICorner")
			cellCorner.CornerRadius = UDim.new(0, 2)
			cellCorner.Parent = cell
		end
	end

	shapeFrame:SetAttribute("ShapeIndex", index)

	local button = Instance.new("TextButton")
	button.Size = UDim2.new(1, 0, 1, 0)
	button.BackgroundTransparency = 1
	button.Text = ""
	button.Parent = shapeFrame

	button.MouseButton1Down:Connect(function()
		if not isDragging and shapeFrame.Parent then
			draggedShape = shape
			draggedShapeUI = shapeFrame
			isDragging = true

			-- Remove the shape from available shapes immediately
			local gameState = GameManager.getGameState()
			for i, availableShape in ipairs(gameState.availableShapes) do
				if availableShape == shape then
					gameState.availableShapes[i] = nil
					break
				end
			end

			-- Hide the UI immediately
			shapeFrame.Visible = false
		end
	end)

	return shapeFrame
end

local function clearPreview()
	local gameState = GameManager.getGameState()
	for _, tile in ipairs(previewTiles) do
		if tile and tile.Parent then
			local tileName = tile.Name
			local tileIndex = tonumber(tileName:match("Tile_(%d+)"))
			if tileIndex then
				local row = math.floor((tileIndex - 1) / 8) + 1
				local col = ((tileIndex - 1) % 8) + 1

				if gameState.gridState[row][col] == 0 then
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
	previewTiles = {}
end

local function showPreview(shape, startRow, startCol)
	clearPreview()
	if not shape or not startRow or not startCol then return end

	local width, height = BlockShapes.getShapeSize(shape)
	local gameState = GameManager.getGameState()
	local canPlace = BlockShapes.canPlaceShape(gameState.gridState, shape, startRow, startCol)

	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1

				if gridRow >= 1 and gridRow &lt;= 8 and gridCol >= 1 and gridCol &lt;= 8 then
					local tileIndex = (gridRow - 1) * 8 + gridCol
					local tile = grid:FindFirstChild("Tile_" .. tileIndex)

					if tile and gameState.gridState[gridRow][gridCol] == 0 then
						if canPlace then
							tile.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
							tile.BackgroundTransparency = 0.5
						else
							tile.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
							tile.BackgroundTransparency = 0.5
						end
						table.insert(previewTiles, tile)
					end
				end
			end
		end
	end
end

local function updateShapesDisplay()
	for _, child in ipairs(shapeSelection:GetChildren()) do
		if child:IsA("Frame") and child.Name:match("Shape_") then
			child:Destroy()
		end
	end
	local gameState = GameManager.getGameState()
	for i, shape in ipairs(gameState.availableShapes) do
		createShapeUI(shape, i)
	end
end

local function getGridPositionFromMouse(mousePos)
	local gridPos = grid.AbsolutePosition
	local gridSize = grid.AbsoluteSize
	local relativeX = mousePos.X - gridPos.X
	local relativeY = mousePos.Y - gridPos.Y

	if relativeX &lt; 0 or relativeY &lt; 0 or relativeX > gridSize.X or relativeY > gridSize.Y then
		return nil, nil
	end

	local col = math.floor(relativeX / (gridSize.X / 8)) + 1
	local row = math.floor(relativeY / (gridSize.Y / 8)) + 1

	-- Ensure we're within bounds
	row = math.max(1, math.min(8, row))
	col = math.max(1, math.min(8, col))



	return row, col
end

UserInputService.InputChanged:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseMovement and isDragging and draggedShape then
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)
		showPreview(draggedShape, row, col)
	end
end)

UserInputService.InputEnded:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseButton1 and isDragging and draggedShape then
		clearPreview()
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)

		if row and col then
			local oldScore = GameManager.getGameState().currentScore
			if GameManager.placeShape(draggedShape, row, col) then
				local newScore = GameManager.getGameState().currentScore
				gameStats.blocksPlaced = gameStats.blocksPlaced + 1

				-- Successfully placed - destroy the UI and clear drag state immediately
				if draggedShapeUI and draggedShapeUI.Parent then
					draggedShapeUI:Destroy()
				end

				-- Clear drag state immediately so block can't be placed again
				draggedShape = nil
				draggedShapeUI = nil
				isDragging = false

				if newScore > oldScore then
					gameStats.linesCleared = gameStats.linesCleared + 1
					flashClearedLines()

					local screenShake = game:GetService("TweenService"):Create(
						grid,
						TweenInfo.new(0.1, Enum.EasingStyle.Bounce, Enum.EasingDirection.InOut, 0, true),
						{Position = grid.Position + UDim2.new(0, 3, 0, 0)}
					)
					screenShake:Play()

					wait(0.3)
				end

				updateGridVisual()
				updateScoreDisplay()
				updateShapesDisplay()
				checkGameOver()
			else
				-- Failed to place - restore the shape
				local gameState = GameManager.getGameState()
				for i, availableShape in ipairs(gameState.availableShapes) do
					if availableShape == nil then
						gameState.availableShapes[i] = draggedShape
						break
					end
				end

				if draggedShapeUI then
					draggedShapeUI.Visible = true
					draggedShapeUI.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
				end
			end
		else
			-- Not dropped on grid - restore the shape
			local gameState = GameManager.getGameState()
			for i, availableShape in ipairs(gameState.availableShapes) do
				if availableShape == nil then
					gameState.availableShapes[i] = draggedShape
					break
				end
			end

			if draggedShapeUI then
				draggedShapeUI.Visible = true
				draggedShapeUI.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
			end
		end

		-- Always clear drag state at the end
		draggedShape = nil
		draggedShapeUI = nil
		isDragging = false
	end
end)

local function createGameOverScreen()
	local gameOverFrame = Instance.new("Frame")
	gameOverFrame.Name = "GameOverFrame"
	gameOverFrame.Size = UDim2.new(0.6, 0, 0.5, 0)
	gameOverFrame.Position = UDim2.new(0.2, 0, 0.25, 0)
	gameOverFrame.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
	gameOverFrame.BorderSizePixel = 0
	gameOverFrame.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = gameOverFrame

	local gameOverText = Instance.new("TextLabel")
	gameOverText.Size = UDim2.new(1, 0, 0.3, 0)
	gameOverText.Position = UDim2.new(0, 0, 0.1, 0)
	gameOverText.BackgroundTransparency = 1
	gameOverText.Text = "GAME OVER!"
	gameOverText.TextColor3 = Color3.fromRGB(255, 100, 100)
	gameOverText.TextScaled = true
	gameOverText.Font = Enum.Font.SourceSansBold
	gameOverText.Parent = gameOverFrame

	local finalScoreText = Instance.new("TextLabel")
	finalScoreText.Size = UDim2.new(1, 0, 0.2, 0)
	finalScoreText.Position = UDim2.new(0, 0, 0.4, 0)
	finalScoreText.BackgroundTransparency = 1
	finalScoreText.Text = "Final Score: " .. GameManager.getGameState().currentScore
	finalScoreText.TextColor3 = Color3.fromRGB(255, 255, 255)
	finalScoreText.TextScaled = true
	finalScoreText.Font = Enum.Font.SourceSans
	finalScoreText.Parent = gameOverFrame

	local restartButton = Instance.new("TextButton")
	restartButton.Size = UDim2.new(0.4, 0, 0.15, 0)
	restartButton.Position = UDim2.new(0.3, 0, 0.7, 0)
	restartButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
	restartButton.Text = "Play Again"
	restartButton.TextColor3 = Color3.fromRGB(0, 0, 0)
	restartButton.TextScaled = true
	restartButton.Font = Enum.Font.SourceSansBold
	restartButton.BorderSizePixel = 0
	restartButton.Parent = gameOverFrame

	local buttonCorner = Instance.new("UICorner")
	buttonCorner.CornerRadius = UDim.new(0, 6)
	buttonCorner.Parent = restartButton

	restartButton.MouseButton1Click:Connect(function()
		GameManager.resetGame()
		gameOverFrame:Destroy()
		updateGridVisual()
		updateScoreDisplay()
		updateShapesDisplay()
	end)

	local popTween = game:GetService("TweenService"):Create(
		gameOverFrame,
		TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{Size = UDim2.new(0.6, 0, 0.5, 0)}
	)

	gameOverFrame.Size = UDim2.new(0, 0, 0, 0)
	gameOverFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
	popTween:Play()
	popTween.Completed:Connect(function()
		gameOverFrame.Position = UDim2.new(0.2, 0, 0.25, 0)
	end)
end

local function checkGameOver()
	if not GameManager.getGameState().gameActive then
		sendGameStats()
		createGameOverScreen()
	end
end



leaderboardButton.MouseButton1Click:Connect(function()
	local leaderboardData = getLeaderboardEvent:InvokeServer()
	LeaderboardUI.updateLeaderboard(leaderboardFrame, leaderboardScrollFrame, leaderboardData)
	LeaderboardUI.showLeaderboard(leaderboardFrame)
end)

local function sendGameStats()
	local finalStats = {
		finalScore = GameManager.getGameState().currentScore,
		linesCleared = gameStats.linesCleared,
		blocksPlaced = gameStats.blocksPlaced,
		playTime = tick() - gameStats.startTime
	}
	updateScoreEvent:FireServer(finalStats)
end

updateScoreDisplay()
updateGridVisual()
updateShapesDisplay()</string>
        </Properties>
        <Item class="ModuleScript" referent="13">
          <Properties>
            <string name="Name">LeaderboardUI</string>
            <string name="Source"><![CDATA[local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local ColorThemes = require(ReplicatedStorage.Shared.ColorThemes)

local LeaderboardUI = {}

function LeaderboardUI.createLeaderboardFrame(parent)
	local leaderboardFrame = Instance.new("Frame")
	leaderboardFrame.Name = "LeaderboardFrame"
	leaderboardFrame.Size = UDim2.new(0.4, 0, 0.7, 0)
	leaderboardFrame.Position = UDim2.new(0.3, 0, 0.15, 0)
	leaderboardFrame.BackgroundColor3 = ColorThemes.getColor("background")
	leaderboardFrame.BorderSizePixel = 0
	leaderboardFrame.Visible = false
	leaderboardFrame.Parent = parent
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = leaderboardFrame
	
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0.1, 0)
	title.Position = UDim2.new(0, 0, 0, 0)
	title.BackgroundTransparency = 1
	title.Text = "🏆 LEADERBOARD 🏆"
	title.TextColor3 = Color3.fromRGB(255, 215, 0)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.Parent = leaderboardFrame
	
	local scrollFrame = Instance.new("ScrollingFrame")
	scrollFrame.Size = UDim2.new(0.9, 0, 0.75, 0)
	scrollFrame.Position = UDim2.new(0.05, 0, 0.12, 0)
	scrollFrame.BackgroundColor3 = ColorThemes.getColor("gridBackground")
	scrollFrame.BorderSizePixel = 0
	scrollFrame.ScrollBarThickness = 6
	scrollFrame.Parent = leaderboardFrame
	
	local scrollCorner = Instance.new("UICorner")
	scrollCorner.CornerRadius = UDim.new(0, 8)
	scrollCorner.Parent = scrollFrame
	
	local listLayout = Instance.new("UIListLayout")
	listLayout.Padding = UDim.new(0, 2)
	listLayout.Parent = scrollFrame
	
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0.2, 0, 0.08, 0)
	closeButton.Position = UDim2.new(0.4, 0, 0.9, 0)
	closeButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
	closeButton.Text = "Close"
	closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.BorderSizePixel = 0
	closeButton.Parent = leaderboardFrame
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton
	
	closeButton.MouseButton1Click:Connect(function()
		LeaderboardUI.hideLeaderboard(leaderboardFrame)
	end)
	
	return leaderboardFrame, scrollFrame
end

function LeaderboardUI.createLeaderboardEntry(parent, rank, playerName, score, isCurrentPlayer)
	local entry = Instance.new("Frame")
	entry.Size = UDim2.new(1, 0, 0, 40)
	entry.BackgroundColor3 = isCurrentPlayer and Color3.fromRGB(100, 150, 255) or ColorThemes.getColor("shapeFrame")
	entry.BorderSizePixel = 0
	entry.Parent = parent
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = entry
	
	local rankLabel = Instance.new("TextLabel")
	rankLabel.Size = UDim2.new(0.15, 0, 1, 0)
	rankLabel.Position = UDim2.new(0, 0, 0, 0)
	rankLabel.BackgroundTransparency = 1
	rankLabel.Text = "#" .. rank
	rankLabel.TextColor3 = rank <= 3 and Color3.fromRGB(255, 215, 0) or Color3.fromRGB(255, 255, 255)
	rankLabel.TextScaled = true
	rankLabel.Font = Enum.Font.SourceSansBold
	rankLabel.Parent = entry
	
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Size = UDim2.new(0.55, 0, 1, 0)
	nameLabel.Position = UDim2.new(0.15, 0, 0, 0)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = playerName
	nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	nameLabel.TextScaled = true
	nameLabel.Font = Enum.Font.SourceSans
	nameLabel.TextXAlignment = Enum.TextXAlignment.Left
	nameLabel.Parent = entry
	
	local scoreLabel = Instance.new("TextLabel")
	scoreLabel.Size = UDim2.new(0.3, 0, 1, 0)
	scoreLabel.Position = UDim2.new(0.7, 0, 0, 0)
	scoreLabel.BackgroundTransparency = 1
	scoreLabel.Text = tostring(score)
	scoreLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	scoreLabel.TextScaled = true
	scoreLabel.Font = Enum.Font.SourceSansBold
	scoreLabel.TextXAlignment = Enum.TextXAlignment.Right
	scoreLabel.Parent = entry
	
	if rank <= 3 then
		local trophy = Instance.new("TextLabel")
		trophy.Size = UDim2.new(0, 20, 0, 20)
		trophy.Position = UDim2.new(0, -25, 0.5, -10)
		trophy.BackgroundTransparency = 1
		trophy.Text = rank == 1 and "🥇" or (rank == 2 and "🥈" or "🥉")
		trophy.TextScaled = true
		trophy.Parent = entry
	end
	
	return entry
end

function LeaderboardUI.updateLeaderboard(leaderboardFrame, scrollFrame, leaderboardData)
	for _, child in ipairs(scrollFrame:GetChildren()) do
		if child:IsA("Frame") then
			child:Destroy()
		end
	end
	
	local currentPlayer = Players.LocalPlayer
	
	for _, entry in ipairs(leaderboardData) do
		local isCurrentPlayer = entry.name == currentPlayer.Name
		LeaderboardUI.createLeaderboardEntry(
			scrollFrame,
			entry.rank,
			entry.name,
			entry.score,
			isCurrentPlayer
		)
	end
	
	scrollFrame.CanvasSize = UDim2.new(0, 0, 0, #leaderboardData * 42)
end

function LeaderboardUI.showLeaderboard(leaderboardFrame)
	leaderboardFrame.Visible = true
	leaderboardFrame.Size = UDim2.new(0, 0, 0, 0)
	leaderboardFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
	
	local tween = TweenService:Create(
		leaderboardFrame,
		TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{
			Size = UDim2.new(0.4, 0, 0.7, 0),
			Position = UDim2.new(0.3, 0, 0.15, 0)
		}
	)
	
	tween:Play()
end

function LeaderboardUI.hideLeaderboard(leaderboardFrame)
	local tween = TweenService:Create(
		leaderboardFrame,
		TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
		{
			Size = UDim2.new(0, 0, 0, 0),
			Position = UDim2.new(0.5, 0, 0.5, 0)
		}
	)
	
	tween:Play()
	tween.Completed:Connect(function()
		leaderboardFrame.Visible = false
	end)
end

return LeaderboardUI
]]></string>
          </Properties>
        </Item>
      </Item>
    </Item>
  </Item>
  <Item class="Workspace" referent="14">
    <Properties>
      <string name="Name">Workspace</string>
      <bool name="FilteringEnabled">true</bool>
      <bool name="NeedsPivotMigration">false</bool>
    </Properties>
    <Item class="Part" referent="15">
      <Properties>
        <string name="Name">Baseplate</string>
        <bool name="Anchored">true</bool>
        <Color3uint8 name="Color3uint8">6512483</Color3uint8>
        <bool name="Locked">true</bool>
        <Vector3 name="Position">
          <X>0</X>
          <Y>-10</Y>
          <Z>0</Z>
        </Vector3>
        <Vector3 name="size">
          <X>512</X>
          <Y>20</Y>
          <Z>512</Z>
        </Vector3>
      </Properties>
    </Item>
  </Item>
</roblox>