<roblox version="4">
  <Item class="Lighting" referent="0">
    <Properties>
      <string name="Name">Lighting</string>
      <Color3 name="Ambient">
        <R>0</R>
        <G>0</G>
        <B>0</B>
      </Color3>
      <float name="Brightness">2</float>
      <bool name="GlobalShadows">true</bool>
      <bool name="Outlines">false</bool>
      <token name="Technology">1</token>
    </Properties>
  </Item>
  <Item class="ReplicatedStorage" referent="1">
    <Properties>
      <string name="Name">ReplicatedStorage</string>
    </Properties>
    <Item class="Folder" referent="2">
      <Properties>
        <string name="Name">Shared</string>
      </Properties>
      <Item class="ModuleScript" referent="3">
        <Properties>
          <string name="Name">BlockShapes</string>
          <string name="Source"><![CDATA[-- Block Shapes for Block Blast Game
local BlockShapes = {}

-- Define all possible block shapes (similar to Tetris pieces)
BlockShapes.SHAPES = {
	-- Single block
	{
		name = "Single",
		pattern = {
			{1}
		},
		color = Color3.fromRGB(255, 100, 100)
	},
	
	-- 2x1 Line
	{
		name = "Line2",
		pattern = {
			{1, 1}
		},
		color = Color3.fromRGB(100, 255, 100)
	},
	
	-- 3x1 Line
	{
		name = "Line3",
		pattern = {
			{1, 1, 1}
		},
		color = Color3.fromRGB(100, 100, 255)
	},
	
	-- 4x1 Line
	{
		name = "Line4",
		pattern = {
			{1, 1, 1, 1}
		},
		color = Color3.fromRGB(255, 255, 100)
	},
	
	-- 5x1 Line
	{
		name = "Line5",
		pattern = {
			{1, 1, 1, 1, 1}
		},
		color = Color3.fromRGB(255, 100, 255)
	},
	
	-- 2x2 Square
	{
		name = "Square2x2",
		pattern = {
			{1, 1},
			{1, 1}
		},
		color = Color3.fromRGB(100, 255, 255)
	},
	
	-- 3x3 Square
	{
		name = "Square3x3",
		pattern = {
			{1, 1, 1},
			{1, 1, 1},
			{1, 1, 1}
		},
		color = Color3.fromRGB(255, 150, 100)
	},
	
	-- L-Shape
	{
		name = "L_Shape",
		pattern = {
			{1, 0},
			{1, 0},
			{1, 1}
		},
		color = Color3.fromRGB(150, 255, 150)
	},
	
	-- Reverse L-Shape
	{
		name = "L_Reverse",
		pattern = {
			{0, 1},
			{0, 1},
			{1, 1}
		},
		color = Color3.fromRGB(255, 150, 255)
	},
	
	-- T-Shape
	{
		name = "T_Shape",
		pattern = {
			{1, 1, 1},
			{0, 1, 0}
		},
		color = Color3.fromRGB(150, 150, 255)
	},
	
	-- Small L
	{
		name = "Small_L",
		pattern = {
			{1, 0},
			{1, 1}
		},
		color = Color3.fromRGB(255, 200, 150)
	},
	
	-- Corner piece
	{
		name = "Corner",
		pattern = {
			{1, 1},
			{1, 0}
		},
		color = Color3.fromRGB(200, 255, 200)
	}
}

-- Generate a random block shape
function BlockShapes.getRandomShape()
	local randomIndex = math.random(1, #BlockShapes.SHAPES)
	return BlockShapes.SHAPES[randomIndex]
end

-- Generate 3 random shapes for the player to choose from
function BlockShapes.generateThreeShapes()
	local shapes = {}
	for i = 1, 3 do
		shapes[i] = BlockShapes.getRandomShape()
	end
	return shapes
end

-- Get the size of a shape (width, height)
function BlockShapes.getShapeSize(shape)
	local height = #shape.pattern
	local width = 0
	
	for _, row in ipairs(shape.pattern) do
		width = math.max(width, #row)
	end
	
	return width, height
end

-- Check if a shape can fit at a specific position on the grid
function BlockShapes.canPlaceShape(gridState, shape, startRow, startCol)
	local width, height = BlockShapes.getShapeSize(shape)
	
	-- Check bounds
	if startRow + height - 1 > 8 or startCol + width - 1 > 8 then
		return false
	end
	
	-- Check for collisions
	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1
				
				if gridState[gridRow] and gridState[gridRow][gridCol] == 1 then
					return false
				end
			end
		end
	end
	
	return true
end

return BlockShapes
]]></string>
        </Properties>
      </Item>
      <Item class="ModuleScript" referent="4">
        <Properties>
          <string name="Name">GameManager</string>
          <string name="Source"><![CDATA[-- Game Manager for Block Blast
local GameManager = {}

-- Game state
GameManager.currentScore = 0
GameManager.highestScore = 0
GameManager.gridState = {}
GameManager.availableShapes = {}
GameManager.gameActive = true

-- Initialize the game
function GameManager.init()
	-- Initialize 8x8 grid state (0 = empty, 1 = filled)
	GameManager.gridState = {}
	for row = 1, 8 do
		GameManager.gridState[row] = {}
		for col = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
	end
	
	-- Load highest score from DataStore (placeholder for now)
	GameManager.highestScore = 0
	GameManager.currentScore = 0
	GameManager.gameActive = true
	
	-- Generate initial shapes
	local BlockShapes = require(script.Parent.BlockShapes)
	GameManager.availableShapes = BlockShapes.generateThreeShapes()
end

-- Place a shape on the grid
function GameManager.placeShape(shape, startRow, startCol)
	local BlockShapes = require(script.Parent.BlockShapes)
	
	-- Check if placement is valid
	if not BlockShapes.canPlaceShape(GameManager.gridState, shape, startRow, startCol) then
		return false
	end
	
	-- Place the shape
	local width, height = BlockShapes.getShapeSize(shape)
	local blocksPlaced = 0
	
	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1
				GameManager.gridState[gridRow][gridCol] = 1
				blocksPlaced = blocksPlaced + 1
			end
		end
	end
	
	-- Add points for placing blocks
	GameManager.addScore(blocksPlaced * 10)
	
	-- Check for line clears
	GameManager.checkAndClearLines()
	
	-- Generate new shape if this was one of the available shapes
	GameManager.replaceUsedShape(shape)
	
	return true
end

-- Check and clear complete lines (rows and columns)
function GameManager.checkAndClearLines()
	local linesCleared = 0
	local rowsToCheck = {}
	local colsToCheck = {}
	
	-- Check for complete rows
	for row = 1, 8 do
		local isComplete = true
		for col = 1, 8 do
			if GameManager.gridState[row][col] == 0 then
				isComplete = false
				break
			end
		end
		if isComplete then
			table.insert(rowsToCheck, row)
		end
	end
	
	-- Check for complete columns
	for col = 1, 8 do
		local isComplete = true
		for row = 1, 8 do
			if GameManager.gridState[row][col] == 0 then
				isComplete = false
				break
			end
		end
		if isComplete then
			table.insert(colsToCheck, col)
		end
	end
	
	-- Clear complete rows
	for _, row in ipairs(rowsToCheck) do
		for col = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
		linesCleared = linesCleared + 1
	end
	
	-- Clear complete columns
	for _, col in ipairs(colsToCheck) do
		for row = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
		linesCleared = linesCleared + 1
	end
	
	-- Award bonus points for clearing lines
	if linesCleared > 0 then
		local bonus = linesCleared * 100
		if linesCleared > 1 then
			bonus = bonus * 2 -- Double bonus for multiple lines
		end
		GameManager.addScore(bonus)
	end
	
	return linesCleared
end

-- Add score and update highest score
function GameManager.addScore(points)
	GameManager.currentScore = GameManager.currentScore + points
	
	if GameManager.currentScore > GameManager.highestScore then
		GameManager.highestScore = GameManager.currentScore
		-- TODO: Save to DataStore
	end
end

-- Replace a used shape with a new random one
function GameManager.replaceUsedShape(usedShape)
	local BlockShapes = require(script.Parent.BlockShapes)
	
	-- Find and replace the used shape
	for i, shape in ipairs(GameManager.availableShapes) do
		if shape == usedShape then
			GameManager.availableShapes[i] = BlockShapes.getRandomShape()
			break
		end
	end
	
	-- Check if any shapes can still be placed (game over condition)
	GameManager.checkGameOver()
end

-- Check if the game is over (no more valid moves)
function GameManager.checkGameOver()
	local BlockShapes = require(script.Parent.BlockShapes)
	
	for _, shape in ipairs(GameManager.availableShapes) do
		-- Try to place this shape anywhere on the grid
		for row = 1, 8 do
			for col = 1, 8 do
				if BlockShapes.canPlaceShape(GameManager.gridState, shape, row, col) then
					return false -- Game is not over
				end
			end
		end
	end
	
	-- No valid moves found
	GameManager.gameActive = false
	return true
end

-- Reset the game
function GameManager.resetGame()
	GameManager.init()
end

-- Get current game state for UI updates
function GameManager.getGameState()
	return {
		currentScore = GameManager.currentScore,
		highestScore = GameManager.highestScore,
		gridState = GameManager.gridState,
		availableShapes = GameManager.availableShapes,
		gameActive = GameManager.gameActive
	}
end

return GameManager
]]></string>
        </Properties>
      </Item>
    </Item>
  </Item>
  <Item class="ServerScriptService" referent="5">
    <Properties>
      <string name="Name">ServerScriptService</string>
    </Properties>
    <Item class="Script" referent="6">
      <Properties>
        <string name="Name">Server</string>
        <token name="RunContext">0</token>
        <string name="Source">print("Hello world, from server!")</string>
      </Properties>
    </Item>
  </Item>
  <Item class="SoundService" referent="7">
    <Properties>
      <string name="Name">SoundService</string>
      <bool name="RespectFilteringEnabled">true</bool>
    </Properties>
  </Item>
  <Item class="StarterPlayer" referent="8">
    <Properties>
      <string name="Name">StarterPlayer</string>
    </Properties>
    <Item class="StarterPlayerScripts" referent="9">
      <Properties>
        <string name="Name">StarterPlayerScripts</string>
      </Properties>
      <Item class="LocalScript" referent="10">
        <Properties>
          <string name="Name">Client</string>
          <string name="Source">-- Block Blast Client Script
local Players = Players or game:GetService("Players")
local ReplicatedStorage = ReplicatedStorage or game:GetService("ReplicatedStorage")
local UserInputService = UserInputService or game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for the game modules
local BlockShapes = require(ReplicatedStorage.Shared.BlockShapes)
local GameManager = require(ReplicatedStorage.Shared.GameManager)

-- Initialize the game
GameManager.init()

-- Get UI elements
local function waitForPath(parent, ...)
	local current = parent
	for _, name in ipairs({...}) do
		current = current:WaitForChild(name)
	end
	return current
end

-- Wait for UI elements to load
local starterGui = waitForPath(playerGui, "StarterGui")
local normal = waitForPath(starterGui, "Normal")
local normalGamePlay = waitForPath(normal, "NormalGamePlay")
local grid = waitForPath(normalGamePlay, "Grid")

-- Get score elements (assuming they exist)
local currentScoreLabel = normalGamePlay:FindFirstChild("CurrentScore")
local highestScoreLabel = normalGamePlay:FindFirstChild("HighestScore")

-- Create shape selection area if it doesn't exist
local shapeSelection = normalGamePlay:FindFirstChild("ShapeSelection")
if not shapeSelection then
	shapeSelection = Instance.new("Frame")
	shapeSelection.Name = "ShapeSelection"
	shapeSelection.Size = UDim2.new(0.8, 0, 0.15, 0)
	shapeSelection.Position = UDim2.new(0.1, 0, 0.8, 0)
	shapeSelection.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	shapeSelection.BorderSizePixel = 0
	shapeSelection.Parent = normalGamePlay

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = shapeSelection

	local layout = Instance.new("UIListLayout")
	layout.FillDirection = Enum.FillDirection.Horizontal
	layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	layout.VerticalAlignment = Enum.VerticalAlignment.Center
	layout.Padding = UDim.new(0, 20)
	layout.Parent = shapeSelection
end

-- Variables for drag and drop
local draggedShape = nil
local draggedShapeUI = nil
local originalPosition = nil

-- Function to update score display
local function updateScoreDisplay()
	local gameState = GameManager.getGameState()

	if currentScoreLabel then
		currentScoreLabel.Text = "Score: " .. gameState.currentScore
	end

	if highestScoreLabel then
		highestScoreLabel.Text = "Best: " .. gameState.highestScore
	end
end

-- Function to update grid visual
local function updateGridVisual()
	local gameState = GameManager.getGameState()

	for row = 1, 8 do
		for col = 1, 8 do
			local tileIndex = (row - 1) * 8 + col
			local tile = grid:FindFirstChild("Tile_" .. tileIndex)

			if tile then
				if gameState.gridState[row][col] == 1 then
					tile.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
					tile.BackgroundTransparency = 0
				else
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
end

-- Function to create a visual representation of a shape
local function createShapeUI(shape, index)
	local shapeFrame = Instance.new("Frame")
	shapeFrame.Name = "Shape_" .. index
	shapeFrame.Size = UDim2.new(0, 80, 0, 80)
	shapeFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	shapeFrame.BorderSizePixel = 0
	shapeFrame.Parent = shapeSelection

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = shapeFrame

	-- Create grid layout for the shape
	local shapeGrid = Instance.new("Frame")
	shapeGrid.Size = UDim2.new(0.8, 0, 0.8, 0)
	shapeGrid.Position = UDim2.new(0.1, 0, 0.1, 0)
	shapeGrid.BackgroundTransparency = 1
	shapeGrid.Parent = shapeFrame

	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellPadding = UDim2.new(0, 1, 0, 1)
	gridLayout.Parent = shapeGrid

	-- Calculate grid size based on shape
	local width, height = BlockShapes.getShapeSize(shape)
	gridLayout.CellSize = UDim2.new(1/width, -1, 1/height, -1)
	gridLayout.FillDirectionMaxCells = width

	-- Create the shape pattern
	for row = 1, height do
		for col = 1, width do
			local cell = Instance.new("Frame")
			cell.BorderSizePixel = 0
			cell.Parent = shapeGrid

			if shape.pattern[row][col] == 1 then
				cell.BackgroundColor3 = shape.color
				cell.BackgroundTransparency = 0
			else
				cell.BackgroundTransparency = 1
			end

			local cellCorner = Instance.new("UICorner")
			cellCorner.CornerRadius = UDim.new(0, 2)
			cellCorner.Parent = cell
		end
	end

	-- Store shape data
	shapeFrame:SetAttribute("ShapeIndex", index)

	-- Add click detection
	local button = Instance.new("TextButton")
	button.Size = UDim2.new(1, 0, 1, 0)
	button.BackgroundTransparency = 1
	button.Text = ""
	button.Parent = shapeFrame

	-- Handle shape selection
	button.MouseButton1Down:Connect(function()
		draggedShape = shape
		draggedShapeUI = shapeFrame
		originalPosition = shapeFrame.Position

		-- Visual feedback
		shapeFrame.BackgroundColor3 = Color3.fromRGB(80, 80, 80)
	end)

	return shapeFrame
end

-- Function to update available shapes display
local function updateShapesDisplay()
	-- Clear existing shapes
	for _, child in ipairs(shapeSelection:GetChildren()) do
		if child:IsA("Frame") and child.Name:match("Shape_") then
			child:Destroy()
		end
	end

	-- Create new shapes
	local gameState = GameManager.getGameState()
	for i, shape in ipairs(gameState.availableShapes) do
		createShapeUI(shape, i)
	end
end

-- Function to get grid position from mouse position
local function getGridPositionFromMouse(mousePos)
	local gridPos = grid.AbsolutePosition
	local gridSize = grid.AbsoluteSize

	local relativeX = mousePos.X - gridPos.X
	local relativeY = mousePos.Y - gridPos.Y

	if relativeX &lt; 0 or relativeY &lt; 0 or relativeX > gridSize.X or relativeY > gridSize.Y then
		return nil, nil
	end

	local col = math.floor(relativeX / (gridSize.X / 8)) + 1
	local row = math.floor(relativeY / (gridSize.Y / 8)) + 1

	return row, col
end

-- Handle mouse release (drop shape)
UserInputService.InputEnded:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseButton1 and draggedShape then
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)

		if row and col then
			-- Try to place the shape
			if GameManager.placeShape(draggedShape, row, col) then
				-- Successfully placed
				updateGridVisual()
				updateScoreDisplay()
				updateShapesDisplay()
				checkGameOverUI()

				-- Remove the used shape UI
				if draggedShapeUI then
					draggedShapeUI:Destroy()
				end
			else
				-- Invalid placement, return shape to original position
				if draggedShapeUI then
					draggedShapeUI.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
				end
			end
		else
			-- Not dropped on grid, return to original position
			if draggedShapeUI then
				draggedShapeUI.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
			end
		end

		-- Reset drag state
		draggedShape = nil
		draggedShapeUI = nil
		originalPosition = nil
	end
end)

-- Add reset button
local resetButton = normalGamePlay:FindFirstChild("ResetButton")
if not resetButton then
	resetButton = Instance.new("TextButton")
	resetButton.Name = "ResetButton"
	resetButton.Size = UDim2.new(0.15, 0, 0.06, 0)
	resetButton.Position = UDim2.new(0.425, 0, 0.05, 0)
	resetButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
	resetButton.Text = "Reset Game"
	resetButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	resetButton.TextScaled = true
	resetButton.Font = Enum.Font.SourceSansBold
	resetButton.BorderSizePixel = 0
	resetButton.Parent = normalGamePlay

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = resetButton

	resetButton.MouseButton1Click:Connect(function()
		GameManager.resetGame()
		updateScoreDisplay()
		updateGridVisual()
		updateShapesDisplay()
		print("Game reset!")
	end)
end

-- Game over detection
local function checkGameOverUI()
	local gameState = GameManager.getGameState()
	if not gameState.gameActive then
		-- Show game over message
		local gameOverFrame = normalGamePlay:FindFirstChild("GameOverFrame")
		if not gameOverFrame then
			gameOverFrame = Instance.new("Frame")
			gameOverFrame.Name = "GameOverFrame"
			gameOverFrame.Size = UDim2.new(0.4, 0, 0.3, 0)
			gameOverFrame.Position = UDim2.new(0.3, 0, 0.35, 0)
			gameOverFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
			gameOverFrame.BackgroundTransparency = 0.3
			gameOverFrame.BorderSizePixel = 0
			gameOverFrame.Parent = normalGamePlay

			local corner = Instance.new("UICorner")
			corner.CornerRadius = UDim.new(0, 8)
			corner.Parent = gameOverFrame

			local gameOverText = Instance.new("TextLabel")
			gameOverText.Size = UDim2.new(1, 0, 0.5, 0)
			gameOverText.Position = UDim2.new(0, 0, 0.1, 0)
			gameOverText.BackgroundTransparency = 1
			gameOverText.Text = "Game Over!"
			gameOverText.TextColor3 = Color3.fromRGB(255, 255, 255)
			gameOverText.TextScaled = true
			gameOverText.Font = Enum.Font.SourceSansBold
			gameOverText.Parent = gameOverFrame

			local finalScoreText = Instance.new("TextLabel")
			finalScoreText.Size = UDim2.new(1, 0, 0.3, 0)
			finalScoreText.Position = UDim2.new(0, 0, 0.5, 0)
			finalScoreText.BackgroundTransparency = 1
			finalScoreText.Text = "Final Score: " .. gameState.currentScore
			finalScoreText.TextColor3 = Color3.fromRGB(200, 200, 200)
			finalScoreText.TextScaled = true
			finalScoreText.Font = Enum.Font.SourceSans
			finalScoreText.Parent = gameOverFrame
		end
	else
		-- Remove game over frame if game is active
		local gameOverFrame = normalGamePlay:FindFirstChild("GameOverFrame")
		if gameOverFrame then
			gameOverFrame:Destroy()
		end
	end
end

print("Block Blast game initialized!")
updateScoreDisplay()
updateGridVisual()
updateShapesDisplay()
checkGameOverUI()</string>
        </Properties>
      </Item>
    </Item>
  </Item>
  <Item class="Workspace" referent="11">
    <Properties>
      <string name="Name">Workspace</string>
      <bool name="FilteringEnabled">true</bool>
      <bool name="NeedsPivotMigration">false</bool>
    </Properties>
    <Item class="Part" referent="12">
      <Properties>
        <string name="Name">Baseplate</string>
        <bool name="Anchored">true</bool>
        <Color3uint8 name="Color3uint8">6512483</Color3uint8>
        <bool name="Locked">true</bool>
        <Vector3 name="Position">
          <X>0</X>
          <Y>-10</Y>
          <Z>0</Z>
        </Vector3>
        <Vector3 name="size">
          <X>512</X>
          <Y>20</Y>
          <Z>512</Z>
        </Vector3>
      </Properties>
    </Item>
  </Item>
</roblox>