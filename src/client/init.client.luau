-- Block Blast Client Script
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for the game modules
local BlockShapes = require(ReplicatedStorage.Shared.BlockShapes)
local GameManager = require(ReplicatedStorage.Shared.GameManager)

-- Initialize the game
print("🎮 Starting Block Blast initialization...")
GameManager.init()
print("✅ GameManager initialized")

-- Wait for your existing UI structure
local normalUI = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay")
local currentScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("CurrentScore")
local highestScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("HighestScore")
local grid = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("Grid")

print("✅ Found existing UI elements!")
print("Grid position:", grid.Position)
print("Grid size:", grid.Size)

-- Create shape selection area in your existing UI
local shapeSelection = normalUI:FindFirstChild("ShapeSelection")
if not shapeSelection then
	shapeSelection = Instance.new("Frame")
	shapeSelection.Name = "ShapeSelection"
	shapeSelection.Size = UDim2.new(0.8, 0, 0.15, 0)
	shapeSelection.Position = UDim2.new(0.1, 0, 0.8, 0)
	shapeSelection.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	shapeSelection.BorderSizePixel = 0
	shapeSelection.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = shapeSelection

	local layout = Instance.new("UIListLayout")
	layout.FillDirection = Enum.FillDirection.Horizontal
	layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	layout.VerticalAlignment = Enum.VerticalAlignment.Center
	layout.Padding = UDim.new(0, 20)
	layout.Parent = shapeSelection

	print("✅ Created ShapeSelection area")
end

-- Variables for drag and drop
local draggedShape = nil
local draggedShapeUI = nil
local originalPosition = nil

-- Function to update score display
local function updateScoreDisplay()
	local gameState = GameManager.getGameState()

	if currentScoreLabel then
		currentScoreLabel.Text = "Score: " .. gameState.currentScore
	end

	if highestScoreLabel then
		highestScoreLabel.Text = "Best: " .. gameState.highestScore
	end
end

-- Function to update grid visual
local function updateGridVisual()
	local gameState = GameManager.getGameState()

	for row = 1, 8 do
		for col = 1, 8 do
			local tileIndex = (row - 1) * 8 + col
			local tile = grid:FindFirstChild("Tile_" .. tileIndex)

			if tile then
				if gameState.gridState[row][col] == 1 then
					tile.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
					tile.BackgroundTransparency = 0
				else
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
end

-- Function to create a visual representation of a shape
local function createShapeUI(shape, index)
	local shapeFrame = Instance.new("Frame")
	shapeFrame.Name = "Shape_" .. index
	shapeFrame.Size = UDim2.new(0, 80, 0, 80)
	shapeFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	shapeFrame.BorderSizePixel = 0
	shapeFrame.Parent = shapeSelection

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = shapeFrame

	-- Create grid layout for the shape
	local shapeGrid = Instance.new("Frame")
	shapeGrid.Size = UDim2.new(0.8, 0, 0.8, 0)
	shapeGrid.Position = UDim2.new(0.1, 0, 0.1, 0)
	shapeGrid.BackgroundTransparency = 1
	shapeGrid.Parent = shapeFrame

	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellPadding = UDim2.new(0, 1, 0, 1)
	gridLayout.Parent = shapeGrid

	-- Calculate grid size based on shape
	local width, height = BlockShapes.getShapeSize(shape)
	gridLayout.CellSize = UDim2.new(1/width, -1, 1/height, -1)
	gridLayout.FillDirectionMaxCells = width

	-- Create the shape pattern
	for row = 1, height do
		for col = 1, width do
			local cell = Instance.new("Frame")
			cell.BorderSizePixel = 0
			cell.Parent = shapeGrid

			if shape.pattern[row][col] == 1 then
				cell.BackgroundColor3 = shape.color
				cell.BackgroundTransparency = 0
			else
				cell.BackgroundTransparency = 1
			end

			local cellCorner = Instance.new("UICorner")
			cellCorner.CornerRadius = UDim.new(0, 2)
			cellCorner.Parent = cell
		end
	end

	-- Store shape data
	shapeFrame:SetAttribute("ShapeIndex", index)

	-- Add click detection
	local button = Instance.new("TextButton")
	button.Size = UDim2.new(1, 0, 1, 0)
	button.BackgroundTransparency = 1
	button.Text = ""
	button.Parent = shapeFrame

	-- Handle shape selection
	button.MouseButton1Down:Connect(function()
		draggedShape = shape
		draggedShapeUI = shapeFrame
		originalPosition = shapeFrame.Position

		-- Visual feedback
		shapeFrame.BackgroundColor3 = Color3.fromRGB(80, 80, 80)
	end)

	return shapeFrame
end

-- Function to update available shapes display
local function updateShapesDisplay()
	-- Clear existing shapes
	for _, child in ipairs(shapeSelection:GetChildren()) do
		if child:IsA("Frame") and child.Name:match("Shape_") then
			child:Destroy()
		end
	end

	-- Create new shapes
	local gameState = GameManager.getGameState()
	for i, shape in ipairs(gameState.availableShapes) do
		createShapeUI(shape, i)
	end
end

-- Function to get grid position from mouse position
local function getGridPositionFromMouse(mousePos)
	local gridPos = grid.AbsolutePosition
	local gridSize = grid.AbsoluteSize

	local relativeX = mousePos.X - gridPos.X
	local relativeY = mousePos.Y - gridPos.Y

	if relativeX < 0 or relativeY < 0 or relativeX > gridSize.X or relativeY > gridSize.Y then
		return nil, nil
	end

	local col = math.floor(relativeX / (gridSize.X / 8)) + 1
	local row = math.floor(relativeY / (gridSize.Y / 8)) + 1

	return row, col
end

-- Handle mouse release (drop shape)
UserInputService.InputEnded:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseButton1 and draggedShape then
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)

		if row and col then
			-- Try to place the shape
			if GameManager.placeShape(draggedShape, row, col) then
				-- Successfully placed
				updateGridVisual()
				updateScoreDisplay()
				updateShapesDisplay()
				checkGameOverUI()

				-- Remove the used shape UI
				if draggedShapeUI then
					draggedShapeUI:Destroy()
				end
			else
				-- Invalid placement, return shape to original position
				if draggedShapeUI then
					draggedShapeUI.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
				end
			end
		else
			-- Not dropped on grid, return to original position
			if draggedShapeUI then
				draggedShapeUI.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
			end
		end

		-- Reset drag state
		draggedShape = nil
		draggedShapeUI = nil
		originalPosition = nil
	end
end)

-- Add reset button to your existing UI
local resetButton = normalUI:FindFirstChild("ResetButton")
if not resetButton then
	resetButton = Instance.new("TextButton")
	resetButton.Name = "ResetButton"
	resetButton.Size = UDim2.new(0.15, 0, 0.06, 0)
	resetButton.Position = UDim2.new(0.425, 0, 0.05, 0)
	resetButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
	resetButton.Text = "Reset Game"
	resetButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	resetButton.TextScaled = true
	resetButton.Font = Enum.Font.SourceSansBold
	resetButton.BorderSizePixel = 0
	resetButton.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = resetButton

	resetButton.MouseButton1Click:Connect(function()
		GameManager.resetGame()
		updateScoreDisplay()
		updateGridVisual()
		updateShapesDisplay()
		print("Game reset!")
	end)

	print("✅ Created Reset button")
end

-- Game over detection
local function checkGameOverUI()
	local gameState = GameManager.getGameState()
	if not gameState.gameActive then
		-- Show game over message
		local gameOverFrame = normalUI:FindFirstChild("GameOverFrame")
		if not gameOverFrame then
			gameOverFrame = Instance.new("Frame")
			gameOverFrame.Name = "GameOverFrame"
			gameOverFrame.Size = UDim2.new(0.4, 0, 0.3, 0)
			gameOverFrame.Position = UDim2.new(0.3, 0, 0.35, 0)
			gameOverFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
			gameOverFrame.BackgroundTransparency = 0.3
			gameOverFrame.BorderSizePixel = 0
			gameOverFrame.Parent = normalUI

			local corner = Instance.new("UICorner")
			corner.CornerRadius = UDim.new(0, 8)
			corner.Parent = gameOverFrame

			local gameOverText = Instance.new("TextLabel")
			gameOverText.Size = UDim2.new(1, 0, 0.5, 0)
			gameOverText.Position = UDim2.new(0, 0, 0.1, 0)
			gameOverText.BackgroundTransparency = 1
			gameOverText.Text = "Game Over!"
			gameOverText.TextColor3 = Color3.fromRGB(255, 255, 255)
			gameOverText.TextScaled = true
			gameOverText.Font = Enum.Font.SourceSansBold
			gameOverText.Parent = gameOverFrame

			local finalScoreText = Instance.new("TextLabel")
			finalScoreText.Size = UDim2.new(1, 0, 0.3, 0)
			finalScoreText.Position = UDim2.new(0, 0, 0.5, 0)
			finalScoreText.BackgroundTransparency = 1
			finalScoreText.Text = "Final Score: " .. gameState.currentScore
			finalScoreText.TextColor3 = Color3.fromRGB(200, 200, 200)
			finalScoreText.TextScaled = true
			finalScoreText.Font = Enum.Font.SourceSans
			finalScoreText.Parent = gameOverFrame
		end
	else
		-- Remove game over frame if game is active
		local gameOverFrame = normalUI:FindFirstChild("GameOverFrame")
		if gameOverFrame then
			gameOverFrame:Destroy()
		end
	end
end

print("Block Blast game initialized!")
updateScoreDisplay()
updateGridVisual()
updateShapesDisplay()
checkGameOverUI()