local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local BlockShapes = require(ReplicatedStorage.Shared.BlockShapes)
local GameManager = require(ReplicatedStorage.Shared.GameManager)

GameManager.init()

local normalUI = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay")
local currentScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("CurrentScore")
local highestScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("HighestScore")
local grid = playerGui:WaitForChild("Normal"):WaitFor<PERSON>hild("NormalGamePlay"):WaitFor<PERSON>hild("Grid")

local shapeSelection = normalUI:FindFirstChild("ShapeSelection")
if not shapeSelection then
	shapeSelection = Instance.new("Frame")
	shapeSelection.Name = "ShapeSelection"
	shapeSelection.Size = UDim2.new(0.8, 0, 0.15, 0)
	shapeSelection.Position = UDim2.new(0.1, 0, 0.8, 0)
	shapeSelection.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	shapeSelection.BorderSizePixel = 0
	shapeSelection.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = shapeSelection

	local layout = Instance.new("UIListLayout")
	layout.FillDirection = Enum.FillDirection.Horizontal
	layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	layout.VerticalAlignment = Enum.VerticalAlignment.Center
	layout.Padding = UDim.new(0, 20)
	layout.Parent = shapeSelection
end

local draggedShape = nil
local draggedShapeUI = nil
local isDragging = false
local previewTiles = {}

local function updateScoreDisplay()
	local gameState = GameManager.getGameState()
	if currentScoreLabel then
		currentScoreLabel.Text = "Score: " .. gameState.currentScore
	end
	if highestScoreLabel then
		highestScoreLabel.Text = "Best: " .. gameState.highestScore
	end
end

local function updateGridVisual()
	local gameState = GameManager.getGameState()
	for row = 1, 8 do
		for col = 1, 8 do
			local tileIndex = (row - 1) * 8 + col
			local tile = grid:FindFirstChild("Tile_" .. tileIndex)
			if tile then
				if gameState.gridState[row][col] == 1 then
					tile.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
					tile.BackgroundTransparency = 0
				else
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
end

local function flashClearedLines()
	wait(0.1)
	for row = 1, 8 do
		local isRowComplete = true
		for col = 1, 8 do
			if GameManager.getGameState().gridState[row][col] == 0 then
				isRowComplete = false
				break
			end
		end
		if isRowComplete then
			for col = 1, 8 do
				local tileIndex = (row - 1) * 8 + col
				local tile = grid:FindFirstChild("Tile_" .. tileIndex)
				if tile then
					tile.BackgroundColor3 = Color3.fromRGB(255, 255, 100)
				end
			end
		end
	end

	for col = 1, 8 do
		local isColComplete = true
		for row = 1, 8 do
			if GameManager.getGameState().gridState[row][col] == 0 then
				isColComplete = false
				break
			end
		end
		if isColComplete then
			for row = 1, 8 do
				local tileIndex = (row - 1) * 8 + col
				local tile = grid:FindFirstChild("Tile_" .. tileIndex)
				if tile then
					tile.BackgroundColor3 = Color3.fromRGB(255, 255, 100)
				end
			end
		end
	end
end

local function createShapeUI(shape, index)
	local shapeFrame = Instance.new("Frame")
	shapeFrame.Name = "Shape_" .. index
	shapeFrame.Size = UDim2.new(0, 80, 0, 80)
	shapeFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
	shapeFrame.BorderSizePixel = 0
	shapeFrame.Parent = shapeSelection

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = shapeFrame

	local shapeGrid = Instance.new("Frame")
	shapeGrid.Size = UDim2.new(0.8, 0, 0.8, 0)
	shapeGrid.Position = UDim2.new(0.1, 0, 0.1, 0)
	shapeGrid.BackgroundTransparency = 1
	shapeGrid.Parent = shapeFrame

	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellPadding = UDim2.new(0, 1, 0, 1)
	gridLayout.Parent = shapeGrid

	local width, height = BlockShapes.getShapeSize(shape)
	gridLayout.CellSize = UDim2.new(1/width, -1, 1/height, -1)
	gridLayout.FillDirectionMaxCells = width

	for row = 1, height do
		for col = 1, width do
			local cell = Instance.new("Frame")
			cell.BorderSizePixel = 0
			cell.Parent = shapeGrid

			if shape.pattern[row][col] == 1 then
				cell.BackgroundColor3 = shape.color
				cell.BackgroundTransparency = 0
			else
				cell.BackgroundTransparency = 1
			end

			local cellCorner = Instance.new("UICorner")
			cellCorner.CornerRadius = UDim.new(0, 2)
			cellCorner.Parent = cell
		end
	end

	shapeFrame:SetAttribute("ShapeIndex", index)

	local button = Instance.new("TextButton")
	button.Size = UDim2.new(1, 0, 1, 0)
	button.BackgroundTransparency = 1
	button.Text = ""
	button.Parent = shapeFrame

	button.MouseButton1Down:Connect(function()
		if not isDragging then
			draggedShape = shape
			draggedShapeUI = shapeFrame
			isDragging = true
			shapeFrame.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
		end
	end)

	return shapeFrame
end

local function clearPreview()
	local gameState = GameManager.getGameState()
	for _, tile in ipairs(previewTiles) do
		if tile and tile.Parent then
			local tileName = tile.Name
			local tileIndex = tonumber(tileName:match("Tile_(%d+)"))
			if tileIndex then
				local row = math.floor((tileIndex - 1) / 8) + 1
				local col = ((tileIndex - 1) % 8) + 1

				if gameState.gridState[row][col] == 0 then
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
	previewTiles = {}
end

local function showPreview(shape, startRow, startCol)
	clearPreview()
	if not shape or not startRow or not startCol then return end

	local width, height = BlockShapes.getShapeSize(shape)
	local gameState = GameManager.getGameState()
	local canPlace = BlockShapes.canPlaceShape(gameState.gridState, shape, startRow, startCol)

	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1

				if gridRow >= 1 and gridRow <= 8 and gridCol >= 1 and gridCol <= 8 then
					local tileIndex = (gridRow - 1) * 8 + gridCol
					local tile = grid:FindFirstChild("Tile_" .. tileIndex)

					if tile and gameState.gridState[gridRow][gridCol] == 0 then
						if canPlace then
							tile.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
							tile.BackgroundTransparency = 0.5
						else
							tile.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
							tile.BackgroundTransparency = 0.5
						end
						table.insert(previewTiles, tile)
					end
				end
			end
		end
	end
end

local function updateShapesDisplay()
	for _, child in ipairs(shapeSelection:GetChildren()) do
		if child:IsA("Frame") and child.Name:match("Shape_") then
			child:Destroy()
		end
	end
	local gameState = GameManager.getGameState()
	for i, shape in ipairs(gameState.availableShapes) do
		createShapeUI(shape, i)
	end
end

local function getGridPositionFromMouse(mousePos)
	local gridPos = grid.AbsolutePosition
	local gridSize = grid.AbsoluteSize
	local relativeX = mousePos.X - gridPos.X
	local relativeY = mousePos.Y - gridPos.Y

	if relativeX < 0 or relativeY < 0 or relativeX > gridSize.X or relativeY > gridSize.Y then
		return nil, nil
	end

	local col = math.floor(relativeX / (gridSize.X / 8)) + 1
	local row = math.floor(relativeY / (gridSize.Y / 8)) + 1
	return row, col
end

UserInputService.InputChanged:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseMovement and isDragging and draggedShape then
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)
		showPreview(draggedShape, row, col)
	end
end)

UserInputService.InputEnded:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseButton1 and isDragging and draggedShape then
		clearPreview()
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)

		if row and col then
			if GameManager.placeShape(draggedShape, row, col) then
				flashClearedLines()
				wait(0.2)
				updateGridVisual()
				updateScoreDisplay()
				updateShapesDisplay()
				if draggedShapeUI then
					draggedShapeUI:Destroy()
				end
			else
				if draggedShapeUI then
					draggedShapeUI.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
				end
			end
		else
			if draggedShapeUI then
				draggedShapeUI.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
			end
		end

		draggedShape = nil
		draggedShapeUI = nil
		isDragging = false
	end
end)

updateScoreDisplay()
updateGridVisual()
updateShapesDisplay()