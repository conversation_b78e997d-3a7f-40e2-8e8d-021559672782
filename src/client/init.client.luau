local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local UserInputService = game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

local remoteEvents = ReplicatedStorage:WaitForChild("RemoteEvents")
local updateScoreEvent = remoteEvents:WaitForChild("UpdateScore")
local getLeaderboardEvent = remoteEvents:WaitForChild("GetLeaderboard")
local getPlayerDataEvent = remoteEvents:WaitForChild("GetPlayerData")
local setThemeEvent = remoteEvents:WaitForChild("SetTheme")

local BlockShapes = require(ReplicatedStorage.Shared.BlockShapes)
local GameManager = require(ReplicatedStorage.Shared.GameManager)
local ColorThemes = require(ReplicatedStorage.Shared.ColorThemes)
local ParticleEffects = require(ReplicatedStorage.Shared.ParticleEffects)
local LeaderboardUI = require(script.LeaderboardUI)

GameManager.init()

local normalUI = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay")
local currentScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("CurrentScore")
local highestScoreLabel = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("HighestScore")
local grid = playerGui:WaitForChild("Normal"):WaitForChild("NormalGamePlay"):WaitForChild("Grid")

local playerData = getPlayerDataEvent:InvokeServer()
if playerData and playerData.selectedTheme then
	ColorThemes.setTheme(playerData.selectedTheme)
end

local gameStats = {
	linesCleared = 0,
	blocksPlaced = 0,
	startTime = tick()
}

local shapeSelection = normalUI:FindFirstChild("ShapeSelection")
if not shapeSelection then
	shapeSelection = Instance.new("Frame")
	shapeSelection.Name = "ShapeSelection"
	shapeSelection.Size = UDim2.new(0.8, 0, 0.15, 0)
	shapeSelection.Position = UDim2.new(0.1, 0, 0.8, 0)
	shapeSelection.BackgroundColor3 = ColorThemes.getColor("shapeSelection")
	shapeSelection.BorderSizePixel = 0
	shapeSelection.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = shapeSelection

	local layout = Instance.new("UIListLayout")
	layout.FillDirection = Enum.FillDirection.Horizontal
	layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	layout.VerticalAlignment = Enum.VerticalAlignment.Center
	layout.Padding = UDim.new(0, 20)
	layout.Parent = shapeSelection
end

local leaderboardFrame, leaderboardScrollFrame = LeaderboardUI.createLeaderboardFrame(normalUI)

local themeButton = normalUI:FindFirstChild("ThemeButton")
if not themeButton then
	themeButton = Instance.new("TextButton")
	themeButton.Name = "ThemeButton"
	themeButton.Size = UDim2.new(0.1, 0, 0.06, 0)
	themeButton.Position = UDim2.new(0.05, 0, 0.15, 0)
	themeButton.BackgroundColor3 = ColorThemes.getColor("shapeFrame")
	themeButton.Text = "🎨"
	themeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	themeButton.TextScaled = true
	themeButton.Font = Enum.Font.SourceSansBold
	themeButton.BorderSizePixel = 0
	themeButton.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = themeButton
end

local leaderboardButton = normalUI:FindFirstChild("LeaderboardButton")
if not leaderboardButton then
	leaderboardButton = Instance.new("TextButton")
	leaderboardButton.Name = "LeaderboardButton"
	leaderboardButton.Size = UDim2.new(0.1, 0, 0.06, 0)
	leaderboardButton.Position = UDim2.new(0.85, 0, 0.15, 0)
	leaderboardButton.BackgroundColor3 = ColorThemes.getColor("shapeFrame")
	leaderboardButton.Text = "🏆"
	leaderboardButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	leaderboardButton.TextScaled = true
	leaderboardButton.Font = Enum.Font.SourceSansBold
	leaderboardButton.BorderSizePixel = 0
	leaderboardButton.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = leaderboardButton
end

local draggedShape = nil
local draggedShapeUI = nil
local isDragging = false
local previewTiles = {}

local function createScorePopup(points)
	local popup = Instance.new("TextLabel")
	popup.Size = UDim2.new(0, 100, 0, 40)
	popup.Position = UDim2.new(0.5, -50, 0.4, 0)
	popup.BackgroundTransparency = 1
	popup.Text = "+" .. points
	popup.TextColor3 = Color3.fromRGB(255, 255, 100)
	popup.TextScaled = true
	popup.Font = Enum.Font.SourceSansBold
	popup.TextStrokeTransparency = 0
	popup.TextStrokeColor3 = Color3.fromRGB(0, 0, 0)
	popup.Parent = normalUI

	local tween = game:GetService("TweenService"):Create(
		popup,
		TweenInfo.new(1, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Position = UDim2.new(0.5, -50, 0.2, 0),
			TextTransparency = 1,
			TextStrokeTransparency = 1
		}
	)

	tween:Play()
	tween.Completed:Connect(function()
		popup:Destroy()
	end)
end

local lastScore = 0

local function updateScoreDisplay()
	local gameState = GameManager.getGameState()
	if currentScoreLabel then
		currentScoreLabel.Text = "Score: " .. gameState.currentScore

		if gameState.currentScore > lastScore then
			local pointsGained = gameState.currentScore - lastScore
			if pointsGained >= 100 then
				createScorePopup(pointsGained)
			end
		end
		lastScore = gameState.currentScore
	end
	if highestScoreLabel then
		highestScoreLabel.Text = "Best: " .. gameState.highestScore
	end
end

local function updateGridVisual()
	local gameState = GameManager.getGameState()
	for row = 1, 8 do
		for col = 1, 8 do
			local tileIndex = (row - 1) * 8 + col
			local tile = grid:FindFirstChild("Tile_" .. tileIndex)
			if tile then
				if gameState.gridState[row][col] == 1 then
					tile.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
					tile.BackgroundTransparency = 0
				else
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
end

local function createEnhancedParticleEffect(tile, linesCleared)
	local tilePos = UDim2.new(0.5, 0, 0.5, 0)
	local color = ColorThemes.getColor("particle")

	ParticleEffects.createExplosion(tile, tilePos, color, 8)

	if linesCleared > 1 then
		wait(0.1)
		ParticleEffects.createStarBurst(tile, tilePos, color, 6)
	end

	if linesCleared > 2 then
		wait(0.1)
		ParticleEffects.createSparkles(tile, tilePos, color, 15)
	end
end

local function flashClearedLines()
	local gameState = GameManager.getGameState()
	local clearedTiles = {}

	for row = 1, 8 do
		local isRowComplete = true
		for col = 1, 8 do
			if gameState.gridState[row][col] == 0 then
				isRowComplete = false
				break
			end
		end
		if isRowComplete then
			for col = 1, 8 do
				local tileIndex = (row - 1) * 8 + col
				local tile = grid:FindFirstChild("Tile_" .. tileIndex)
				if tile then
					tile.BackgroundColor3 = Color3.fromRGB(255, 255, 100)
					table.insert(clearedTiles, tile)
				end
			end
		end
	end

	for col = 1, 8 do
		local isColComplete = true
		for row = 1, 8 do
			if gameState.gridState[row][col] == 0 then
				isColComplete = false
				break
			end
		end
		if isColComplete then
			for row = 1, 8 do
				local tileIndex = (row - 1) * 8 + col
				local tile = grid:FindFirstChild("Tile_" .. tileIndex)
				if tile then
					tile.BackgroundColor3 = Color3.fromRGB(255, 255, 100)
					table.insert(clearedTiles, tile)
				end
			end
		end
	end

	wait(0.1)

	for _, tile in ipairs(clearedTiles) do
		createParticleEffect(tile)

		local shakeTween = game:GetService("TweenService"):Create(
			tile,
			TweenInfo.new(0.1, Enum.EasingStyle.Bounce, Enum.EasingDirection.InOut, 0, true),
			{Position = tile.Position + UDim2.new(0, 2, 0, 0)}
		)
		shakeTween:Play()

		local colorTween = game:GetService("TweenService"):Create(
			tile,
			TweenInfo.new(0.3, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
			{
				BackgroundColor3 = Color3.fromRGB(255, 100, 100),
				BackgroundTransparency = 0.8
			}
		)
		colorTween:Play()
	end

	wait(0.2)
end

local function createShapeUI(shape, index)
	local shapeFrame = Instance.new("Frame")
	shapeFrame.Name = "Shape_" .. index
	shapeFrame.Size = UDim2.new(0, 80, 0, 80)
	shapeFrame.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
	shapeFrame.BorderSizePixel = 0
	shapeFrame.Parent = shapeSelection

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = shapeFrame

	local shapeGrid = Instance.new("Frame")
	shapeGrid.Size = UDim2.new(0.8, 0, 0.8, 0)
	shapeGrid.Position = UDim2.new(0.1, 0, 0.1, 0)
	shapeGrid.BackgroundTransparency = 1
	shapeGrid.Parent = shapeFrame

	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellPadding = UDim2.new(0, 1, 0, 1)
	gridLayout.Parent = shapeGrid

	local width, height = BlockShapes.getShapeSize(shape)
	gridLayout.CellSize = UDim2.new(1/width, -1, 1/height, -1)
	gridLayout.FillDirectionMaxCells = width

	for row = 1, height do
		for col = 1, width do
			local cell = Instance.new("Frame")
			cell.BorderSizePixel = 0
			cell.Parent = shapeGrid

			if shape.pattern[row][col] == 1 then
				cell.BackgroundColor3 = shape.color
				cell.BackgroundTransparency = 0
			else
				cell.BackgroundTransparency = 1
			end

			local cellCorner = Instance.new("UICorner")
			cellCorner.CornerRadius = UDim.new(0, 2)
			cellCorner.Parent = cell
		end
	end

	shapeFrame:SetAttribute("ShapeIndex", index)

	local button = Instance.new("TextButton")
	button.Size = UDim2.new(1, 0, 1, 0)
	button.BackgroundTransparency = 1
	button.Text = ""
	button.Parent = shapeFrame

	button.MouseButton1Down:Connect(function()
		if not isDragging then
			draggedShape = shape
			draggedShapeUI = shapeFrame
			isDragging = true
			shapeFrame.BackgroundColor3 = Color3.fromRGB(100, 100, 100)
		end
	end)

	return shapeFrame
end

local function clearPreview()
	local gameState = GameManager.getGameState()
	for _, tile in ipairs(previewTiles) do
		if tile and tile.Parent then
			local tileName = tile.Name
			local tileIndex = tonumber(tileName:match("Tile_(%d+)"))
			if tileIndex then
				local row = math.floor((tileIndex - 1) / 8) + 1
				local col = ((tileIndex - 1) % 8) + 1

				if gameState.gridState[row][col] == 0 then
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
	previewTiles = {}
end

local function showPreview(shape, startRow, startCol)
	clearPreview()
	if not shape or not startRow or not startCol then return end

	local width, height = BlockShapes.getShapeSize(shape)
	local gameState = GameManager.getGameState()
	local canPlace = BlockShapes.canPlaceShape(gameState.gridState, shape, startRow, startCol)

	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1

				if gridRow >= 1 and gridRow <= 8 and gridCol >= 1 and gridCol <= 8 then
					local tileIndex = (gridRow - 1) * 8 + gridCol
					local tile = grid:FindFirstChild("Tile_" .. tileIndex)

					if tile and gameState.gridState[gridRow][gridCol] == 0 then
						if canPlace then
							tile.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
							tile.BackgroundTransparency = 0.5
						else
							tile.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
							tile.BackgroundTransparency = 0.5
						end
						table.insert(previewTiles, tile)
					end
				end
			end
		end
	end
end

local function updateShapesDisplay()
	for _, child in ipairs(shapeSelection:GetChildren()) do
		if child:IsA("Frame") and child.Name:match("Shape_") then
			child:Destroy()
		end
	end
	local gameState = GameManager.getGameState()
	for i, shape in ipairs(gameState.availableShapes) do
		createShapeUI(shape, i)
	end
end

local function getGridPositionFromMouse(mousePos)
	local gridPos = grid.AbsolutePosition
	local gridSize = grid.AbsoluteSize
	local relativeX = mousePos.X - gridPos.X
	local relativeY = mousePos.Y - gridPos.Y

	if relativeX < 0 or relativeY < 0 or relativeX > gridSize.X or relativeY > gridSize.Y then
		return nil, nil
	end

	local col = math.floor(relativeX / (gridSize.X / 8)) + 1
	local row = math.floor(relativeY / (gridSize.Y / 8)) + 1
	return row, col
end

UserInputService.InputChanged:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseMovement and isDragging and draggedShape then
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)
		showPreview(draggedShape, row, col)
	end
end)

UserInputService.InputEnded:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseButton1 and isDragging and draggedShape then
		clearPreview()
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)

		if row and col then
			local oldScore = GameManager.getGameState().currentScore
			if GameManager.placeShape(draggedShape, row, col) then
				local newScore = GameManager.getGameState().currentScore
				gameStats.blocksPlaced = gameStats.blocksPlaced + 1

				if newScore > oldScore then
					gameStats.linesCleared = gameStats.linesCleared + 1
					flashClearedLines()

					local screenShake = game:GetService("TweenService"):Create(
						grid,
						TweenInfo.new(0.1, Enum.EasingStyle.Bounce, Enum.EasingDirection.InOut, 0, true),
						{Position = grid.Position + UDim2.new(0, 3, 0, 0)}
					)
					screenShake:Play()

					wait(0.3)
				end

				updateGridVisual()
				updateScoreDisplay()
				updateShapesDisplay()
				checkGameOver()
				if draggedShapeUI then
					draggedShapeUI:Destroy()
				end
			else
				if draggedShapeUI then
					draggedShapeUI.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
				end
			end
		else
			if draggedShapeUI then
				draggedShapeUI.BackgroundColor3 = Color3.fromRGB(60, 60, 60)
			end
		end

		draggedShape = nil
		draggedShapeUI = nil
		isDragging = false
	end
end)

local function createGameOverScreen()
	local gameOverFrame = Instance.new("Frame")
	gameOverFrame.Name = "GameOverFrame"
	gameOverFrame.Size = UDim2.new(0.6, 0, 0.5, 0)
	gameOverFrame.Position = UDim2.new(0.2, 0, 0.25, 0)
	gameOverFrame.BackgroundColor3 = Color3.fromRGB(20, 20, 20)
	gameOverFrame.BorderSizePixel = 0
	gameOverFrame.Parent = normalUI

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = gameOverFrame

	local gameOverText = Instance.new("TextLabel")
	gameOverText.Size = UDim2.new(1, 0, 0.3, 0)
	gameOverText.Position = UDim2.new(0, 0, 0.1, 0)
	gameOverText.BackgroundTransparency = 1
	gameOverText.Text = "GAME OVER!"
	gameOverText.TextColor3 = Color3.fromRGB(255, 100, 100)
	gameOverText.TextScaled = true
	gameOverText.Font = Enum.Font.SourceSansBold
	gameOverText.Parent = gameOverFrame

	local finalScoreText = Instance.new("TextLabel")
	finalScoreText.Size = UDim2.new(1, 0, 0.2, 0)
	finalScoreText.Position = UDim2.new(0, 0, 0.4, 0)
	finalScoreText.BackgroundTransparency = 1
	finalScoreText.Text = "Final Score: " .. GameManager.getGameState().currentScore
	finalScoreText.TextColor3 = Color3.fromRGB(255, 255, 255)
	finalScoreText.TextScaled = true
	finalScoreText.Font = Enum.Font.SourceSans
	finalScoreText.Parent = gameOverFrame

	local restartButton = Instance.new("TextButton")
	restartButton.Size = UDim2.new(0.4, 0, 0.15, 0)
	restartButton.Position = UDim2.new(0.3, 0, 0.7, 0)
	restartButton.BackgroundColor3 = Color3.fromRGB(100, 255, 100)
	restartButton.Text = "Play Again"
	restartButton.TextColor3 = Color3.fromRGB(0, 0, 0)
	restartButton.TextScaled = true
	restartButton.Font = Enum.Font.SourceSansBold
	restartButton.BorderSizePixel = 0
	restartButton.Parent = gameOverFrame

	local buttonCorner = Instance.new("UICorner")
	buttonCorner.CornerRadius = UDim.new(0, 6)
	buttonCorner.Parent = restartButton

	restartButton.MouseButton1Click:Connect(function()
		GameManager.resetGame()
		gameOverFrame:Destroy()
		updateGridVisual()
		updateScoreDisplay()
		updateShapesDisplay()
	end)

	local popTween = game:GetService("TweenService"):Create(
		gameOverFrame,
		TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{Size = UDim2.new(0.6, 0, 0.5, 0)}
	)

	gameOverFrame.Size = UDim2.new(0, 0, 0, 0)
	gameOverFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
	popTween:Play()
	popTween.Completed:Connect(function()
		gameOverFrame.Position = UDim2.new(0.2, 0, 0.25, 0)
	end)
end

local function checkGameOver()
	if not GameManager.getGameState().gameActive then
		sendGameStats()
		createGameOverScreen()
	end
end

local currentThemeIndex = 1
local themeNames = ColorThemes.getThemeNames()

themeButton.MouseButton1Click:Connect(function()
	currentThemeIndex = currentThemeIndex + 1
	if currentThemeIndex > #themeNames then
		currentThemeIndex = 1
	end

	local newTheme = themeNames[currentThemeIndex]
	ColorThemes.setTheme(newTheme)
	setThemeEvent:FireServer(newTheme)

	shapeSelection.BackgroundColor3 = ColorThemes.getColor("shapeSelection")
	themeButton.BackgroundColor3 = ColorThemes.getColor("shapeFrame")
	leaderboardButton.BackgroundColor3 = ColorThemes.getColor("shapeFrame")

	updateGridVisual()
	updateShapesDisplay()
end)

leaderboardButton.MouseButton1Click:Connect(function()
	local leaderboardData = getLeaderboardEvent:InvokeServer()
	LeaderboardUI.updateLeaderboard(leaderboardFrame, leaderboardScrollFrame, leaderboardData)
	LeaderboardUI.showLeaderboard(leaderboardFrame)
end)

local function sendGameStats()
	local finalStats = {
		finalScore = GameManager.getGameState().currentScore,
		linesCleared = gameStats.linesCleared,
		blocksPlaced = gameStats.blocksPlaced,
		playTime = tick() - gameStats.startTime
	}
	updateScoreEvent:FireServer(finalStats)
end

updateScoreDisplay()
updateGridVisual()
updateShapesDisplay()