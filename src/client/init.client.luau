-- Block Blast Client Script
local Players = Players or game:GetService("Players")
local ReplicatedStorage = ReplicatedStorage or game:GetService("ReplicatedStorage")
local UserInputService = UserInputService or game:GetService("UserInputService")

local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- Wait for the game modules
local BlockShapes = require(ReplicatedStorage.Shared.BlockShapes)
local GameManager = require(ReplicatedStorage.Shared.GameManager)

-- Initialize the game
GameManager.init()

-- Create or get UI elements
local function createOrGetUI()
	-- Create main ScreenGui
	local starterGui = playerGui:FindFirstChild("StarterGui")
	if not starterGui then
		starterGui = Instance.new("ScreenGui")
		starterGui.Name = "StarterGui"
		starterGui.Parent = playerGui
		print("Created StarterGui")
	end

	-- Create Normal frame
	local normal = starterGui:FindFirstChild("Normal")
	if not normal then
		normal = Instance.new("ScreenGui")
		normal.Name = "Normal"
		normal.Parent = starterGui
		print("Created Normal")
	end

	-- Create main game frame
	local normalGamePlay = normal:FindFirstChild("NormalGamePlay")
	if not normalGamePlay then
		normalGamePlay = Instance.new("Frame")
		normalGamePlay.Name = "NormalGamePlay"
		normalGamePlay.Size = UDim2.new(1, 0, 1, 0)
		normalGamePlay.Position = UDim2.new(0, 0, 0, 0)
		normalGamePlay.BackgroundColor3 = Color3.fromRGB(25, 25, 25)
		normalGamePlay.BorderSizePixel = 0
		normalGamePlay.Parent = normal
		print("Created NormalGamePlay")
	end

	-- Create grid
	local grid = normalGamePlay:FindFirstChild("Grid")
	if not grid then
		grid = Instance.new("Frame")
		grid.Name = "Grid"
		grid.Size = UDim2.new(0.313, 0, 0.313, 0)
		grid.Position = UDim2.new(0.313, 0, 0.126, 0)
		grid.BackgroundColor3 = Color3.fromRGB(30, 30, 30)
		grid.BorderSizePixel = 0
		grid.Parent = normalGamePlay

		-- Add UIGridLayout
		local layout = Instance.new("UIGridLayout")
		layout.Parent = grid
		print("Created Grid with layout")

		-- Create grid tiles (your existing grid code)
		local rows, cols = 8, 8
		layout.CellPadding = UDim2.new(0, 2, 0, 2)

		-- Wait for the grid to be sized properly
		wait(0.1)

		local gridWidth = grid.AbsoluteSize.X
		local gridHeight = grid.AbsoluteSize.Y

		if gridWidth > 0 and gridHeight > 0 then
			local paddingX = layout.CellPadding.X.Offset
			local paddingY = layout.CellPadding.Y.Offset

			local cellWidth = (gridWidth - (cols - 1) * paddingX) / cols
			local cellHeight = (gridHeight - (rows - 1) * paddingY) / rows

			layout.CellSize = UDim2.new(0, cellWidth, 0, cellHeight)
			layout.FillDirectionMaxCells = cols

			for i = 1, rows * cols do
				local tile = Instance.new("Frame")
				tile.Size = UDim2.new(1, 0, 1, 0)
				tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
				tile.BorderSizePixel = 0
				tile.Name = "Tile_" .. i
				tile.Parent = grid
				tile.BackgroundTransparency = 0.98

				local corner = Instance.new("UICorner")
				corner.CornerRadius = UDim.new(0, 4)
				corner.Parent = tile
			end
			print("Created " .. (rows * cols) .. " grid tiles")
		else
			print("Warning: Grid size is 0, tiles not created yet")
		end
	end

	return starterGui, normal, normalGamePlay, grid
end

local starterGui, normal, normalGamePlay, grid = createOrGetUI()

-- Create score elements
local currentScoreLabel = normalGamePlay:FindFirstChild("CurrentScore")
if not currentScoreLabel then
	currentScoreLabel = Instance.new("TextLabel")
	currentScoreLabel.Name = "CurrentScore"
	currentScoreLabel.Size = UDim2.new(0.2, 0, 0.08, 0)
	currentScoreLabel.Position = UDim2.new(0.05, 0, 0.05, 0)
	currentScoreLabel.BackgroundTransparency = 1
	currentScoreLabel.Text = "Score: 0"
	currentScoreLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	currentScoreLabel.TextScaled = true
	currentScoreLabel.Font = Enum.Font.SourceSansBold
	currentScoreLabel.Parent = normalGamePlay
	print("Created CurrentScore label")
end

local highestScoreLabel = normalGamePlay:FindFirstChild("HighestScore")
if not highestScoreLabel then
	highestScoreLabel = Instance.new("TextLabel")
	highestScoreLabel.Name = "HighestScore"
	highestScoreLabel.Size = UDim2.new(0.2, 0, 0.08, 0)
	highestScoreLabel.Position = UDim2.new(0.75, 0, 0.05, 0)
	highestScoreLabel.BackgroundTransparency = 1
	highestScoreLabel.Text = "Best: 0"
	highestScoreLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	highestScoreLabel.TextScaled = true
	highestScoreLabel.Font = Enum.Font.SourceSansBold
	highestScoreLabel.Parent = normalGamePlay
	print("Created HighestScore label")
end

-- Create shape selection area if it doesn't exist
local shapeSelection = normalGamePlay:FindFirstChild("ShapeSelection")
if not shapeSelection then
	shapeSelection = Instance.new("Frame")
	shapeSelection.Name = "ShapeSelection"
	shapeSelection.Size = UDim2.new(0.8, 0, 0.15, 0)
	shapeSelection.Position = UDim2.new(0.1, 0, 0.8, 0)
	shapeSelection.BackgroundColor3 = Color3.fromRGB(40, 40, 40)
	shapeSelection.BorderSizePixel = 0
	shapeSelection.Parent = normalGamePlay

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 8)
	corner.Parent = shapeSelection

	local layout = Instance.new("UIListLayout")
	layout.FillDirection = Enum.FillDirection.Horizontal
	layout.HorizontalAlignment = Enum.HorizontalAlignment.Center
	layout.VerticalAlignment = Enum.VerticalAlignment.Center
	layout.Padding = UDim.new(0, 20)
	layout.Parent = shapeSelection
end

-- Variables for drag and drop
local draggedShape = nil
local draggedShapeUI = nil
local originalPosition = nil

-- Function to update score display
local function updateScoreDisplay()
	local gameState = GameManager.getGameState()

	if currentScoreLabel then
		currentScoreLabel.Text = "Score: " .. gameState.currentScore
	end

	if highestScoreLabel then
		highestScoreLabel.Text = "Best: " .. gameState.highestScore
	end
end

-- Function to update grid visual
local function updateGridVisual()
	local gameState = GameManager.getGameState()

	for row = 1, 8 do
		for col = 1, 8 do
			local tileIndex = (row - 1) * 8 + col
			local tile = grid:FindFirstChild("Tile_" .. tileIndex)

			if tile then
				if gameState.gridState[row][col] == 1 then
					tile.BackgroundColor3 = Color3.fromRGB(100, 150, 255)
					tile.BackgroundTransparency = 0
				else
					tile.BackgroundColor3 = Color3.fromRGB(200, 200, 200)
					tile.BackgroundTransparency = 0.98
				end
			end
		end
	end
end

-- Function to create a visual representation of a shape
local function createShapeUI(shape, index)
	local shapeFrame = Instance.new("Frame")
	shapeFrame.Name = "Shape_" .. index
	shapeFrame.Size = UDim2.new(0, 80, 0, 80)
	shapeFrame.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
	shapeFrame.BorderSizePixel = 0
	shapeFrame.Parent = shapeSelection

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 6)
	corner.Parent = shapeFrame

	-- Create grid layout for the shape
	local shapeGrid = Instance.new("Frame")
	shapeGrid.Size = UDim2.new(0.8, 0, 0.8, 0)
	shapeGrid.Position = UDim2.new(0.1, 0, 0.1, 0)
	shapeGrid.BackgroundTransparency = 1
	shapeGrid.Parent = shapeFrame

	local gridLayout = Instance.new("UIGridLayout")
	gridLayout.CellPadding = UDim2.new(0, 1, 0, 1)
	gridLayout.Parent = shapeGrid

	-- Calculate grid size based on shape
	local width, height = BlockShapes.getShapeSize(shape)
	gridLayout.CellSize = UDim2.new(1/width, -1, 1/height, -1)
	gridLayout.FillDirectionMaxCells = width

	-- Create the shape pattern
	for row = 1, height do
		for col = 1, width do
			local cell = Instance.new("Frame")
			cell.BorderSizePixel = 0
			cell.Parent = shapeGrid

			if shape.pattern[row][col] == 1 then
				cell.BackgroundColor3 = shape.color
				cell.BackgroundTransparency = 0
			else
				cell.BackgroundTransparency = 1
			end

			local cellCorner = Instance.new("UICorner")
			cellCorner.CornerRadius = UDim.new(0, 2)
			cellCorner.Parent = cell
		end
	end

	-- Store shape data
	shapeFrame:SetAttribute("ShapeIndex", index)

	-- Add click detection
	local button = Instance.new("TextButton")
	button.Size = UDim2.new(1, 0, 1, 0)
	button.BackgroundTransparency = 1
	button.Text = ""
	button.Parent = shapeFrame

	-- Handle shape selection
	button.MouseButton1Down:Connect(function()
		draggedShape = shape
		draggedShapeUI = shapeFrame
		originalPosition = shapeFrame.Position

		-- Visual feedback
		shapeFrame.BackgroundColor3 = Color3.fromRGB(80, 80, 80)
	end)

	return shapeFrame
end

-- Function to update available shapes display
local function updateShapesDisplay()
	-- Clear existing shapes
	for _, child in ipairs(shapeSelection:GetChildren()) do
		if child:IsA("Frame") and child.Name:match("Shape_") then
			child:Destroy()
		end
	end

	-- Create new shapes
	local gameState = GameManager.getGameState()
	for i, shape in ipairs(gameState.availableShapes) do
		createShapeUI(shape, i)
	end
end

-- Function to get grid position from mouse position
local function getGridPositionFromMouse(mousePos)
	local gridPos = grid.AbsolutePosition
	local gridSize = grid.AbsoluteSize

	local relativeX = mousePos.X - gridPos.X
	local relativeY = mousePos.Y - gridPos.Y

	if relativeX < 0 or relativeY < 0 or relativeX > gridSize.X or relativeY > gridSize.Y then
		return nil, nil
	end

	local col = math.floor(relativeX / (gridSize.X / 8)) + 1
	local row = math.floor(relativeY / (gridSize.Y / 8)) + 1

	return row, col
end

-- Handle mouse release (drop shape)
UserInputService.InputEnded:Connect(function(input)
	if input.UserInputType == Enum.UserInputType.MouseButton1 and draggedShape then
		local mousePos = UserInputService:GetMouseLocation()
		local row, col = getGridPositionFromMouse(mousePos)

		if row and col then
			-- Try to place the shape
			if GameManager.placeShape(draggedShape, row, col) then
				-- Successfully placed
				updateGridVisual()
				updateScoreDisplay()
				updateShapesDisplay()
				checkGameOverUI()

				-- Remove the used shape UI
				if draggedShapeUI then
					draggedShapeUI:Destroy()
				end
			else
				-- Invalid placement, return shape to original position
				if draggedShapeUI then
					draggedShapeUI.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
				end
			end
		else
			-- Not dropped on grid, return to original position
			if draggedShapeUI then
				draggedShapeUI.BackgroundColor3 = Color3.fromRGB(50, 50, 50)
			end
		end

		-- Reset drag state
		draggedShape = nil
		draggedShapeUI = nil
		originalPosition = nil
	end
end)

-- Add reset button
local resetButton = normalGamePlay:FindFirstChild("ResetButton")
if not resetButton then
	resetButton = Instance.new("TextButton")
	resetButton.Name = "ResetButton"
	resetButton.Size = UDim2.new(0.15, 0, 0.06, 0)
	resetButton.Position = UDim2.new(0.425, 0, 0.05, 0)
	resetButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
	resetButton.Text = "Reset Game"
	resetButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	resetButton.TextScaled = true
	resetButton.Font = Enum.Font.SourceSansBold
	resetButton.BorderSizePixel = 0
	resetButton.Parent = normalGamePlay

	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = resetButton

	resetButton.MouseButton1Click:Connect(function()
		GameManager.resetGame()
		updateScoreDisplay()
		updateGridVisual()
		updateShapesDisplay()
		print("Game reset!")
	end)
end

-- Game over detection
local function checkGameOverUI()
	local gameState = GameManager.getGameState()
	if not gameState.gameActive then
		-- Show game over message
		local gameOverFrame = normalGamePlay:FindFirstChild("GameOverFrame")
		if not gameOverFrame then
			gameOverFrame = Instance.new("Frame")
			gameOverFrame.Name = "GameOverFrame"
			gameOverFrame.Size = UDim2.new(0.4, 0, 0.3, 0)
			gameOverFrame.Position = UDim2.new(0.3, 0, 0.35, 0)
			gameOverFrame.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
			gameOverFrame.BackgroundTransparency = 0.3
			gameOverFrame.BorderSizePixel = 0
			gameOverFrame.Parent = normalGamePlay

			local corner = Instance.new("UICorner")
			corner.CornerRadius = UDim.new(0, 8)
			corner.Parent = gameOverFrame

			local gameOverText = Instance.new("TextLabel")
			gameOverText.Size = UDim2.new(1, 0, 0.5, 0)
			gameOverText.Position = UDim2.new(0, 0, 0.1, 0)
			gameOverText.BackgroundTransparency = 1
			gameOverText.Text = "Game Over!"
			gameOverText.TextColor3 = Color3.fromRGB(255, 255, 255)
			gameOverText.TextScaled = true
			gameOverText.Font = Enum.Font.SourceSansBold
			gameOverText.Parent = gameOverFrame

			local finalScoreText = Instance.new("TextLabel")
			finalScoreText.Size = UDim2.new(1, 0, 0.3, 0)
			finalScoreText.Position = UDim2.new(0, 0, 0.5, 0)
			finalScoreText.BackgroundTransparency = 1
			finalScoreText.Text = "Final Score: " .. gameState.currentScore
			finalScoreText.TextColor3 = Color3.fromRGB(200, 200, 200)
			finalScoreText.TextScaled = true
			finalScoreText.Font = Enum.Font.SourceSans
			finalScoreText.Parent = gameOverFrame
		end
	else
		-- Remove game over frame if game is active
		local gameOverFrame = normalGamePlay:FindFirstChild("GameOverFrame")
		if gameOverFrame then
			gameOverFrame:Destroy()
		end
	end
end

print("Block Blast game initialized!")
updateScoreDisplay()
updateGridVisual()
updateShapesDisplay()
checkGameOverUI()