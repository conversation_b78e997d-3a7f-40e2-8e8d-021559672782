local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")
local TweenService = game:GetService("TweenService")

local player = Players.LocalPlayer
local playerGui = player:WaitFor<PERSON>hild("PlayerGui")

local ColorThemes = require(ReplicatedStorage.Shared.ColorThemes)

local LeaderboardUI = {}

function LeaderboardUI.createLeaderboardFrame(parent)
	local leaderboardFrame = Instance.new("Frame")
	leaderboardFrame.Name = "LeaderboardFrame"
	leaderboardFrame.Size = UDim2.new(0.4, 0, 0.7, 0)
	leaderboardFrame.Position = UDim2.new(0.3, 0, 0.15, 0)
	leaderboardFrame.BackgroundColor3 = ColorThemes.getColor("background")
	leaderboardFrame.BorderSizePixel = 0
	leaderboardFrame.Visible = false
	leaderboardFrame.Parent = parent
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 12)
	corner.Parent = leaderboardFrame
	
	local title = Instance.new("TextLabel")
	title.Size = UDim2.new(1, 0, 0.1, 0)
	title.Position = UDim2.new(0, 0, 0, 0)
	title.BackgroundTransparency = 1
	title.Text = "🏆 LEADERBOARD 🏆"
	title.TextColor3 = Color3.fromRGB(255, 215, 0)
	title.TextScaled = true
	title.Font = Enum.Font.SourceSansBold
	title.Parent = leaderboardFrame
	
	local scrollFrame = Instance.new("ScrollingFrame")
	scrollFrame.Size = UDim2.new(0.9, 0, 0.75, 0)
	scrollFrame.Position = UDim2.new(0.05, 0, 0.12, 0)
	scrollFrame.BackgroundColor3 = ColorThemes.getColor("gridBackground")
	scrollFrame.BorderSizePixel = 0
	scrollFrame.ScrollBarThickness = 6
	scrollFrame.Parent = leaderboardFrame
	
	local scrollCorner = Instance.new("UICorner")
	scrollCorner.CornerRadius = UDim.new(0, 8)
	scrollCorner.Parent = scrollFrame
	
	local listLayout = Instance.new("UIListLayout")
	listLayout.Padding = UDim.new(0, 2)
	listLayout.Parent = scrollFrame
	
	local closeButton = Instance.new("TextButton")
	closeButton.Size = UDim2.new(0.2, 0, 0.08, 0)
	closeButton.Position = UDim2.new(0.4, 0, 0.9, 0)
	closeButton.BackgroundColor3 = Color3.fromRGB(255, 100, 100)
	closeButton.Text = "Close"
	closeButton.TextColor3 = Color3.fromRGB(255, 255, 255)
	closeButton.TextScaled = true
	closeButton.Font = Enum.Font.SourceSansBold
	closeButton.BorderSizePixel = 0
	closeButton.Parent = leaderboardFrame
	
	local closeCorner = Instance.new("UICorner")
	closeCorner.CornerRadius = UDim.new(0, 6)
	closeCorner.Parent = closeButton
	
	closeButton.MouseButton1Click:Connect(function()
		LeaderboardUI.hideLeaderboard(leaderboardFrame)
	end)
	
	return leaderboardFrame, scrollFrame
end

function LeaderboardUI.createLeaderboardEntry(parent, rank, playerName, score, isCurrentPlayer)
	local entry = Instance.new("Frame")
	entry.Size = UDim2.new(1, 0, 0, 40)
	entry.BackgroundColor3 = isCurrentPlayer and Color3.fromRGB(100, 150, 255) or ColorThemes.getColor("shapeFrame")
	entry.BorderSizePixel = 0
	entry.Parent = parent
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(0, 4)
	corner.Parent = entry
	
	local rankLabel = Instance.new("TextLabel")
	rankLabel.Size = UDim2.new(0.15, 0, 1, 0)
	rankLabel.Position = UDim2.new(0, 0, 0, 0)
	rankLabel.BackgroundTransparency = 1
	rankLabel.Text = "#" .. rank
	rankLabel.TextColor3 = rank <= 3 and Color3.fromRGB(255, 215, 0) or Color3.fromRGB(255, 255, 255)
	rankLabel.TextScaled = true
	rankLabel.Font = Enum.Font.SourceSansBold
	rankLabel.Parent = entry
	
	local nameLabel = Instance.new("TextLabel")
	nameLabel.Size = UDim2.new(0.55, 0, 1, 0)
	nameLabel.Position = UDim2.new(0.15, 0, 0, 0)
	nameLabel.BackgroundTransparency = 1
	nameLabel.Text = playerName
	nameLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	nameLabel.TextScaled = true
	nameLabel.Font = Enum.Font.SourceSans
	nameLabel.TextXAlignment = Enum.TextXAlignment.Left
	nameLabel.Parent = entry
	
	local scoreLabel = Instance.new("TextLabel")
	scoreLabel.Size = UDim2.new(0.3, 0, 1, 0)
	scoreLabel.Position = UDim2.new(0.7, 0, 0, 0)
	scoreLabel.BackgroundTransparency = 1
	scoreLabel.Text = tostring(score)
	scoreLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
	scoreLabel.TextScaled = true
	scoreLabel.Font = Enum.Font.SourceSansBold
	scoreLabel.TextXAlignment = Enum.TextXAlignment.Right
	scoreLabel.Parent = entry
	
	if rank <= 3 then
		local trophy = Instance.new("TextLabel")
		trophy.Size = UDim2.new(0, 20, 0, 20)
		trophy.Position = UDim2.new(0, -25, 0.5, -10)
		trophy.BackgroundTransparency = 1
		trophy.Text = rank == 1 and "🥇" or (rank == 2 and "🥈" or "🥉")
		trophy.TextScaled = true
		trophy.Parent = entry
	end
	
	return entry
end

function LeaderboardUI.updateLeaderboard(leaderboardFrame, scrollFrame, leaderboardData)
	for _, child in ipairs(scrollFrame:GetChildren()) do
		if child:IsA("Frame") then
			child:Destroy()
		end
	end
	
	local currentPlayer = Players.LocalPlayer
	
	for _, entry in ipairs(leaderboardData) do
		local isCurrentPlayer = entry.name == currentPlayer.Name
		LeaderboardUI.createLeaderboardEntry(
			scrollFrame,
			entry.rank,
			entry.name,
			entry.score,
			isCurrentPlayer
		)
	end
	
	scrollFrame.CanvasSize = UDim2.new(0, 0, 0, #leaderboardData * 42)
end

function LeaderboardUI.showLeaderboard(leaderboardFrame)
	leaderboardFrame.Visible = true
	leaderboardFrame.Size = UDim2.new(0, 0, 0, 0)
	leaderboardFrame.Position = UDim2.new(0.5, 0, 0.5, 0)
	
	local tween = TweenService:Create(
		leaderboardFrame,
		TweenInfo.new(0.3, Enum.EasingStyle.Back, Enum.EasingDirection.Out),
		{
			Size = UDim2.new(0.4, 0, 0.7, 0),
			Position = UDim2.new(0.3, 0, 0.15, 0)
		}
	)
	
	tween:Play()
end

function LeaderboardUI.hideLeaderboard(leaderboardFrame)
	local tween = TweenService:Create(
		leaderboardFrame,
		TweenInfo.new(0.2, Enum.EasingStyle.Quad, Enum.EasingDirection.In),
		{
			Size = UDim2.new(0, 0, 0, 0),
			Position = UDim2.new(0.5, 0, 0.5, 0)
		}
	)
	
	tween:Play()
	tween.Completed:Connect(function()
		leaderboardFrame.Visible = false
	end)
end

return LeaderboardUI
