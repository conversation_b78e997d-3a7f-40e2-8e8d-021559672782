local DataStoreService = game:GetService("DataStoreService")
local Players = game:GetService("Players")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local playerDataStore = DataStoreService:GetDataStore("BlockBlastPlayerData")
local leaderboardStore = DataStoreService:GetOrderedDataStore("BlockBlastLeaderboard")

local DataStoreManager = {}

local defaultData = {
	highestScore = 0,
	totalGamesPlayed = 0,
	totalLinesCleared = 0,
	totalBlocksPlaced = 0,
	selectedTheme = "Default",
	achievements = {},
	statistics = {
		bestStreak = 0,
		totalPlayTime = 0,
		averageScore = 0
	}
}

local playerData = {}

function DataStoreManager.loadPlayerData(player)
	local success, data = pcall(function()
		return playerDataStore:GetAsync(player.UserId)
	end)
	
	if success and data then
		playerData[player.UserId] = data
		print("Loaded data for", player.Name)
	else
		playerData[player.UserId] = defaultData
		print("Created new data for", player.Name)
	end
	
	return playerData[player.UserId]
end

function DataStoreManager.savePlayerData(player)
	if not playerData[player.UserId] then return end
	
	local success, error = pcall(function()
		playerDataStore:SetAsync(player.UserId, playerData[player.UserId])
	end)
	
	if success then
		print("Saved data for", player.Name)
	else
		warn("Failed to save data for", player.Name, ":", error)
	end
end

function DataStoreManager.updateHighScore(player, newScore)
	if not playerData[player.UserId] then return end
	
	local data = playerData[player.UserId]
	if newScore > data.highestScore then
		data.highestScore = newScore
		DataStoreManager.savePlayerData(player)
		DataStoreManager.updateLeaderboard(player, newScore)
		return true
	end
	return false
end

function DataStoreManager.updateStats(player, gameData)
	if not playerData[player.UserId] then return end
	
	local data = playerData[player.UserId]
	data.totalGamesPlayed = data.totalGamesPlayed + 1
	data.totalLinesCleared = data.totalLinesCleared + (gameData.linesCleared or 0)
	data.totalBlocksPlaced = data.totalBlocksPlaced + (gameData.blocksPlaced or 0)
	
	if gameData.streak and gameData.streak > data.statistics.bestStreak then
		data.statistics.bestStreak = gameData.streak
	end
	
	data.statistics.averageScore = math.floor(
		(data.statistics.averageScore * (data.totalGamesPlayed - 1) + gameData.finalScore) / data.totalGamesPlayed
	)
	
	DataStoreManager.savePlayerData(player)
end

function DataStoreManager.updateLeaderboard(player, score)
	local success, error = pcall(function()
		leaderboardStore:SetAsync(player.UserId, score)
	end)
	
	if not success then
		warn("Failed to update leaderboard for", player.Name, ":", error)
	end
end

function DataStoreManager.getLeaderboard(count)
	count = count or 10
	local success, pages = pcall(function()
		return leaderboardStore:GetSortedAsync(false, count)
	end)
	
	if success then
		local leaderboard = {}
		local data = pages:GetCurrentPage()
		
		for rank, entry in ipairs(data) do
			local playerName = "Unknown"
			local playerSuccess, player = pcall(function()
				return Players:GetNameFromUserIdAsync(entry.key)
			end)
			
			if playerSuccess then
				playerName = player
			end
			
			table.insert(leaderboard, {
				rank = rank,
				name = playerName,
				score = entry.value,
				userId = entry.key
			})
		end
		
		return leaderboard
	else
		warn("Failed to get leaderboard:", pages)
		return {}
	end
end

function DataStoreManager.getPlayerData(player)
	return playerData[player.UserId] or defaultData
end

function DataStoreManager.setTheme(player, themeName)
	if not playerData[player.UserId] then return end
	
	playerData[player.UserId].selectedTheme = themeName
	DataStoreManager.savePlayerData(player)
end

Players.PlayerAdded:Connect(function(player)
	DataStoreManager.loadPlayerData(player)
end)

Players.PlayerRemoving:Connect(function(player)
	DataStoreManager.savePlayerData(player)
	playerData[player.UserId] = nil
end)

game:BindToClose(function()
	for userId, data in pairs(playerData) do
		local player = Players:GetPlayerByUserId(userId)
		if player then
			DataStoreManager.savePlayerData(player)
		end
	end
end)

return DataStoreManager
