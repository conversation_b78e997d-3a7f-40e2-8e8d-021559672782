local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

local DataStoreManager = require(script.DataStoreManager)

local remoteEvents = Instance.new("Folder")
remoteEvents.Name = "RemoteEvents"
remoteEvents.Parent = ReplicatedStorage

local updateScoreEvent = Instance.new("RemoteEvent")
updateScoreEvent.Name = "UpdateScore"
updateScoreEvent.Parent = remoteEvents

local getLeaderboardEvent = Instance.new("RemoteFunction")
getLeaderboardEvent.Name = "GetLeaderboard"
getLeaderboardEvent.Parent = remoteEvents

local getPlayerDataEvent = Instance.new("RemoteFunction")
getPlayerDataEvent.Name = "GetPlayerData"
getPlayerDataEvent.Parent = remoteEvents

local setThemeEvent = Instance.new("RemoteEvent")
setThemeEvent.Name = "SetTheme"
setThemeEvent.Parent = remoteEvents

updateScoreEvent.OnServerEvent:Connect(function(player, gameData)
	DataStoreManager.updateStats(player, gameData)
	if gameData.finalScore then
		DataStoreManager.updateHighScore(player, gameData.finalScore)
	end
end)

getLeaderboardEvent.OnServerInvoke = function(player)
	return DataStoreManager.getLeaderboard(10)
end

getPlayerDataEvent.OnServerInvoke = function(player)
	return DataStoreManager.getPlayerData(player)
end

setThemeEvent.OnServerEvent:Connect(function(player, themeName)
	DataStoreManager.setTheme(player, themeName)
end)

print("Block Blast server initialized!")