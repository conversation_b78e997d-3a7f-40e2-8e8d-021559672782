local TweenService = game:GetService("TweenService")
local ParticleEffects = {}

function ParticleEffects.createExplosion(parent, position, color, particleCount)
	particleCount = particleCount or 8
	
	for i = 1, particleCount do
		local particle = Instance.new("Frame")
		particle.Size = UDim2.new(0, math.random(4, 8), 0, math.random(4, 8))
		particle.Position = position
		particle.BackgroundColor3 = color
		particle.BorderSizePixel = 0
		particle.Parent = parent
		
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(1, 0)
		corner.Parent = particle
		
		local angle = (i / particleCount) * math.pi * 2
		local distance = math.random(20, 40)
		local endX = math.cos(angle) * distance
		local endY = math.sin(angle) * distance
		
		local tween = TweenService:Create(
			particle,
			TweenInfo.new(
				math.random(50, 80) / 100,
				Enum.EasingStyle.Quad,
				Enum.EasingDirection.Out
			),
			{
				Position = position + UDim2.new(0, endX, 0, endY),
				BackgroundTransparency = 1,
				Size = UDim2.new(0, 2, 0, 2)
			}
		)
		
		tween:Play()
		tween.Completed:Connect(function()
			particle:Destroy()
		end)
	end
end

function ParticleEffects.createStarBurst(parent, position, color, starCount)
	starCount = starCount or 6
	
	for i = 1, starCount do
		local star = Instance.new("Frame")
		star.Size = UDim2.new(0, 6, 0, 6)
		star.Position = position
		star.BackgroundColor3 = color
		star.BorderSizePixel = 0
		star.Parent = parent
		
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(0, 1)
		corner.Parent = star
		
		local angle = (i / starCount) * math.pi * 2
		local distance = math.random(30, 60)
		local endX = math.cos(angle) * distance
		local endY = math.sin(angle) * distance
		
		local tween = TweenService:Create(
			star,
			TweenInfo.new(
				0.8,
				Enum.EasingStyle.Quart,
				Enum.EasingDirection.Out
			),
			{
				Position = position + UDim2.new(0, endX, 0, endY),
				BackgroundTransparency = 1,
				Rotation = math.random(0, 360)
			}
		)
		
		tween:Play()
		tween.Completed:Connect(function()
			star:Destroy()
		end)
	end
end

function ParticleEffects.createRipple(parent, position, color)
	local ripple = Instance.new("Frame")
	ripple.Size = UDim2.new(0, 10, 0, 10)
	ripple.Position = position + UDim2.new(0, -5, 0, -5)
	ripple.BackgroundColor3 = color
	ripple.BackgroundTransparency = 0.5
	ripple.BorderSizePixel = 0
	ripple.Parent = parent
	
	local corner = Instance.new("UICorner")
	corner.CornerRadius = UDim.new(1, 0)
	corner.Parent = ripple
	
	local tween = TweenService:Create(
		ripple,
		TweenInfo.new(0.6, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
		{
			Size = UDim2.new(0, 50, 0, 50),
			Position = position + UDim2.new(0, -25, 0, -25),
			BackgroundTransparency = 1
		}
	)
	
	tween:Play()
	tween.Completed:Connect(function()
		ripple:Destroy()
	end)
end

function ParticleEffects.createSparkles(parent, position, color, sparkleCount)
	sparkleCount = sparkleCount or 12
	
	for i = 1, sparkleCount do
		local sparkle = Instance.new("Frame")
		sparkle.Size = UDim2.new(0, 3, 0, 3)
		sparkle.Position = position + UDim2.new(0, math.random(-10, 10), 0, math.random(-10, 10))
		sparkle.BackgroundColor3 = color
		sparkle.BorderSizePixel = 0
		sparkle.Parent = parent
		
		local corner = Instance.new("UICorner")
		corner.CornerRadius = UDim.new(1, 0)
		corner.Parent = sparkle
		
		local tween = TweenService:Create(
			sparkle,
			TweenInfo.new(
				math.random(30, 60) / 100,
				Enum.EasingStyle.Sine,
				Enum.EasingDirection.Out
			),
			{
				Position = sparkle.Position + UDim2.new(0, math.random(-20, 20), 0, math.random(-30, -10)),
				BackgroundTransparency = 1
			}
		)
		
		tween:Play()
		tween.Completed:Connect(function()
			sparkle:Destroy()
		end)
	end
end

function ParticleEffects.createComboEffect(parent, position, color, comboLevel)
	ParticleEffects.createExplosion(parent, position, color, 12)
	
	wait(0.1)
	ParticleEffects.createStarBurst(parent, position, color, 8)
	
	if comboLevel > 1 then
		wait(0.1)
		ParticleEffects.createRipple(parent, position, color)
	end
	
	if comboLevel > 2 then
		wait(0.1)
		ParticleEffects.createSparkles(parent, position, color, 20)
	end
end

function ParticleEffects.createLineBreakEffect(parent, tiles, color)
	for _, tile in ipairs(tiles) do
		local tilePos = UDim2.new(0.5, 0, 0.5, 0)
		
		ParticleEffects.createExplosion(tile, tilePos, color, 6)
		
		wait(0.05)
	end
end

return ParticleEffects
