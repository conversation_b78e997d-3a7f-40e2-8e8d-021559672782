-- Game Manager for Block Blast
local GameManager = {}

-- Game state
GameManager.currentScore = 0
GameManager.highestScore = 0
GameManager.gridState = {}
GameManager.availableShapes = {}
GameManager.gameActive = true

-- Initialize the game
function GameManager.init()
	-- Initialize 8x8 grid state (0 = empty, 1 = filled)
	GameManager.gridState = {}
	for row = 1, 8 do
		GameManager.gridState[row] = {}
		for col = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
	end
	
	-- Load highest score from DataStore (placeholder for now)
	GameManager.highestScore = 0
	GameManager.currentScore = 0
	GameManager.gameActive = true
	
	-- Generate initial shapes
	local BlockShapes = require(script.Parent.BlockShapes)
	GameManager.availableShapes = BlockShapes.generateThreeShapes()
end

-- Place a shape on the grid
function GameManager.placeShape(shape, startRow, startCol)
	local BlockShapes = require(script.Parent.BlockShapes)
	
	-- Check if placement is valid
	if not BlockShapes.canPlaceShape(GameManager.gridState, shape, startRow, startCol) then
		return false
	end
	
	-- Place the shape
	local width, height = BlockShapes.getShapeSize(shape)

	for row = 1, height do
		for col = 1, width do
			if shape.pattern[row][col] == 1 then
				local gridRow = startRow + row - 1
				local gridCol = startCol + col - 1
				GameManager.gridState[gridRow][gridCol] = 1
			end
		end
	end
	
	-- Check for line clears
	GameManager.checkAndClearLines()
	
	-- Generate new shape if this was one of the available shapes
	GameManager.replaceUsedShape(shape)
	
	return true
end

-- Check and clear complete lines (rows and columns)
function GameManager.checkAndClearLines()
	local linesCleared = 0
	local rowsToCheck = {}
	local colsToCheck = {}
	
	-- Check for complete rows
	for row = 1, 8 do
		local isComplete = true
		for col = 1, 8 do
			if GameManager.gridState[row][col] == 0 then
				isComplete = false
				break
			end
		end
		if isComplete then
			table.insert(rowsToCheck, row)
		end
	end
	
	-- Check for complete columns
	for col = 1, 8 do
		local isComplete = true
		for row = 1, 8 do
			if GameManager.gridState[row][col] == 0 then
				isComplete = false
				break
			end
		end
		if isComplete then
			table.insert(colsToCheck, col)
		end
	end
	
	-- Clear complete rows
	for _, row in ipairs(rowsToCheck) do
		for col = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
		linesCleared = linesCleared + 1
	end
	
	-- Clear complete columns
	for _, col in ipairs(colsToCheck) do
		for row = 1, 8 do
			GameManager.gridState[row][col] = 0
		end
		linesCleared = linesCleared + 1
	end
	
	-- Award points for clearing lines
	if linesCleared > 0 then
		local points = linesCleared * 100
		if linesCleared > 1 then
			points = points * 2
		end
		GameManager.addScore(points)
	end
	
	return linesCleared
end

-- Add score and update highest score
function GameManager.addScore(points)
	GameManager.currentScore = GameManager.currentScore + points
	
	if GameManager.currentScore > GameManager.highestScore then
		GameManager.highestScore = GameManager.currentScore
		-- TODO: Save to DataStore
	end
end

-- Replace a used shape with a new random one
function GameManager.replaceUsedShape(usedShape)
	local BlockShapes = require(script.Parent.BlockShapes)

	-- Find and replace the used shape immediately
	for i, shape in ipairs(GameManager.availableShapes) do
		if shape == usedShape then
			GameManager.availableShapes[i] = BlockShapes.getRandomShape()
			break
		end
	end

	-- If all 3 shapes are used, generate 3 new ones
	local allUsed = true
	for _, shape in ipairs(GameManager.availableShapes) do
		if shape then
			allUsed = false
			break
		end
	end

	if allUsed then
		GameManager.availableShapes = BlockShapes.generateThreeShapes()
	end

	-- Check if any shapes can still be placed (game over condition)
	GameManager.checkGameOver()
end

-- Check if the game is over (no more valid moves)
function GameManager.checkGameOver()
	local BlockShapes = require(script.Parent.BlockShapes)
	
	for _, shape in ipairs(GameManager.availableShapes) do
		-- Try to place this shape anywhere on the grid
		for row = 1, 8 do
			for col = 1, 8 do
				if BlockShapes.canPlaceShape(GameManager.gridState, shape, row, col) then
					return false -- Game is not over
				end
			end
		end
	end
	
	-- No valid moves found
	GameManager.gameActive = false
	return true
end

-- Reset the game
function GameManager.resetGame()
	GameManager.init()
end

-- Get current game state for UI updates
function GameManager.getGameState()
	return {
		currentScore = GameManager.currentScore,
		highestScore = GameManager.highestScore,
		gridState = GameManager.gridState,
		availableShapes = GameManager.availableShapes,
		gameActive = GameManager.gameActive
	}
end

return GameManager
