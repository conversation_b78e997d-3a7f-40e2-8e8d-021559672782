local ColorThemes = {}

ColorThemes.THEMES = {
	Default = {
		name = "Default",
		background = Color3.fromRGB(25, 25, 25),
		gridBackground = Color3.fromRGB(30, 30, 30),
		emptyTile = Color3.fromRGB(200, 200, 200),
		filledTile = Color3.fromRGB(100, 150, 255),
		shapeSelection = Color3.fromRGB(40, 40, 40),
		shapeFrame = Color3.fromRGB(60, 60, 60),
		previewValid = Color3.fromRGB(100, 255, 100),
		previewInvalid = Color3.fromRGB(255, 100, 100),
		clearEffect = Color3.fromRGB(255, 255, 100),
		particle = Color3.fromRGB(255, 255, 100),
		scorePopup = Color3.fromRGB(255, 255, 100)
	},
	
	Ocean = {
		name = "Ocean",
		background = Color3.fromRGB(15, 30, 45),
		gridBackground = Color3.fromRGB(20, 40, 60),
		emptyTile = Color3.fromRGB(100, 150, 200),
		filledTile = Color3.fromRGB(50, 150, 255),
		shapeSelection = Color3.fromRGB(25, 50, 75),
		shapeFrame = Color3.fromRGB(40, 80, 120),
		previewValid = Color3.fromRGB(100, 255, 200),
		previewInvalid = Color3.fromRGB(255, 150, 150),
		clearEffect = Color3.fromRGB(100, 200, 255),
		particle = Color3.fromRGB(150, 220, 255),
		scorePopup = Color3.fromRGB(100, 200, 255)
	},
	
	Sunset = {
		name = "Sunset",
		background = Color3.fromRGB(45, 25, 15),
		gridBackground = Color3.fromRGB(60, 35, 20),
		emptyTile = Color3.fromRGB(200, 150, 100),
		filledTile = Color3.fromRGB(255, 150, 50),
		shapeSelection = Color3.fromRGB(75, 45, 25),
		shapeFrame = Color3.fromRGB(120, 70, 40),
		previewValid = Color3.fromRGB(255, 200, 100),
		previewInvalid = Color3.fromRGB(255, 100, 100),
		clearEffect = Color3.fromRGB(255, 200, 100),
		particle = Color3.fromRGB(255, 180, 120),
		scorePopup = Color3.fromRGB(255, 200, 100)
	},
	
	Forest = {
		name = "Forest",
		background = Color3.fromRGB(20, 35, 20),
		gridBackground = Color3.fromRGB(25, 45, 25),
		emptyTile = Color3.fromRGB(150, 200, 150),
		filledTile = Color3.fromRGB(100, 255, 100),
		shapeSelection = Color3.fromRGB(30, 60, 30),
		shapeFrame = Color3.fromRGB(50, 100, 50),
		previewValid = Color3.fromRGB(150, 255, 150),
		previewInvalid = Color3.fromRGB(255, 150, 150),
		clearEffect = Color3.fromRGB(200, 255, 150),
		particle = Color3.fromRGB(180, 255, 180),
		scorePopup = Color3.fromRGB(200, 255, 150)
	},
	
	Neon = {
		name = "Neon",
		background = Color3.fromRGB(10, 10, 10),
		gridBackground = Color3.fromRGB(15, 15, 15),
		emptyTile = Color3.fromRGB(50, 50, 50),
		filledTile = Color3.fromRGB(255, 0, 255),
		shapeSelection = Color3.fromRGB(20, 20, 20),
		shapeFrame = Color3.fromRGB(30, 30, 30),
		previewValid = Color3.fromRGB(0, 255, 255),
		previewInvalid = Color3.fromRGB(255, 0, 0),
		clearEffect = Color3.fromRGB(255, 0, 255),
		particle = Color3.fromRGB(255, 100, 255),
		scorePopup = Color3.fromRGB(255, 0, 255)
	},
	
	Retro = {
		name = "Retro",
		background = Color3.fromRGB(40, 20, 40),
		gridBackground = Color3.fromRGB(50, 25, 50),
		emptyTile = Color3.fromRGB(150, 100, 150),
		filledTile = Color3.fromRGB(255, 200, 100),
		shapeSelection = Color3.fromRGB(60, 30, 60),
		shapeFrame = Color3.fromRGB(80, 40, 80),
		previewValid = Color3.fromRGB(200, 255, 150),
		previewInvalid = Color3.fromRGB(255, 100, 150),
		clearEffect = Color3.fromRGB(255, 255, 200),
		particle = Color3.fromRGB(255, 220, 150),
		scorePopup = Color3.fromRGB(255, 255, 200)
	}
}

ColorThemes.currentTheme = "Default"

function ColorThemes.setTheme(themeName)
	if ColorThemes.THEMES[themeName] then
		ColorThemes.currentTheme = themeName
		return true
	end
	return false
end

function ColorThemes.getCurrentTheme()
	return ColorThemes.THEMES[ColorThemes.currentTheme]
end

function ColorThemes.getThemeNames()
	local names = {}
	for name, _ in pairs(ColorThemes.THEMES) do
		table.insert(names, name)
	end
	return names
end

function ColorThemes.getColor(colorType)
	local theme = ColorThemes.getCurrentTheme()
	return theme[colorType] or Color3.fromRGB(255, 255, 255)
end

return ColorThemes
